((typeof globalThis !== 'undefined' ? globalThis : self)["makoChunk_teamauth-frontend"] = (typeof globalThis !== 'undefined' ? globalThis : self)["makoChunk_teamauth-frontend"] || []).push([
        ['src/pages/personal-center/index.tsx'],
{ "src/components/InvitationStatus.tsx": function (module, exports, __mako_require__){
/**
 * 邀请状态显示组件
 */ "use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_default = __mako_require__("@swc/helpers/_/_interop_require_default");
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _react = /*#__PURE__*/ _interop_require_default._(__mako_require__("node_modules/react/index.js"));
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
var _api = __mako_require__("src/types/api.ts");
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
/**
 * 邀请状态组件
 */ const InvitationStatusComponent = ({ status, isExpired = false })=>{
    // 如果已过期，优先显示过期状态
    if (isExpired && status === _api.InvitationStatus.PENDING) return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.ExclamationCircleOutlined, {}, void 0, false, {
            fileName: "src/components/InvitationStatus.tsx",
            lineNumber: 31,
            columnNumber: 18
        }, void 0),
        color: "orange",
        children: "已过期"
    }, void 0, false, {
        fileName: "src/components/InvitationStatus.tsx",
        lineNumber: 31,
        columnNumber: 7
    }, this);
    switch(status){
        case _api.InvitationStatus.PENDING:
            return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
                icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.ClockCircleOutlined, {}, void 0, false, {
                    fileName: "src/components/InvitationStatus.tsx",
                    lineNumber: 40,
                    columnNumber: 20
                }, void 0),
                color: "blue",
                children: "待确认"
            }, void 0, false, {
                fileName: "src/components/InvitationStatus.tsx",
                lineNumber: 40,
                columnNumber: 9
            }, this);
        case _api.InvitationStatus.ACCEPTED:
            return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
                icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CheckCircleOutlined, {}, void 0, false, {
                    fileName: "src/components/InvitationStatus.tsx",
                    lineNumber: 46,
                    columnNumber: 20
                }, void 0),
                color: "green",
                children: "已接受"
            }, void 0, false, {
                fileName: "src/components/InvitationStatus.tsx",
                lineNumber: 46,
                columnNumber: 9
            }, this);
        case _api.InvitationStatus.REJECTED:
            return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
                icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CloseCircleOutlined, {}, void 0, false, {
                    fileName: "src/components/InvitationStatus.tsx",
                    lineNumber: 52,
                    columnNumber: 20
                }, void 0),
                color: "red",
                children: "已拒绝"
            }, void 0, false, {
                fileName: "src/components/InvitationStatus.tsx",
                lineNumber: 52,
                columnNumber: 9
            }, this);
        case _api.InvitationStatus.EXPIRED:
            return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
                icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.ExclamationCircleOutlined, {}, void 0, false, {
                    fileName: "src/components/InvitationStatus.tsx",
                    lineNumber: 58,
                    columnNumber: 20
                }, void 0),
                color: "orange",
                children: "已过期"
            }, void 0, false, {
                fileName: "src/components/InvitationStatus.tsx",
                lineNumber: 58,
                columnNumber: 9
            }, this);
        case _api.InvitationStatus.CANCELLED:
            return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
                icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.StopOutlined, {}, void 0, false, {
                    fileName: "src/components/InvitationStatus.tsx",
                    lineNumber: 64,
                    columnNumber: 20
                }, void 0),
                color: "default",
                children: "已取消"
            }, void 0, false, {
                fileName: "src/components/InvitationStatus.tsx",
                lineNumber: 64,
                columnNumber: 9
            }, this);
        default:
            return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
                color: "default",
                children: "未知状态"
            }, void 0, false, {
                fileName: "src/components/InvitationStatus.tsx",
                lineNumber: 70,
                columnNumber: 9
            }, this);
    }
};
_c = InvitationStatusComponent;
var _default = InvitationStatusComponent;
var _c;
$RefreshReg$(_c, "InvitationStatusComponent");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
"src/pages/personal-center/PersonalInfo.module.css?asmodule": function (module, exports, __mako_require__){
"use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
"";
var _default = {
    "contentArea": `contentArea-_n2x4BDb`,
    "contactCard": `contactCard-iYaWi3ca`,
    "statsCard": `statsCard-RbylBLDK`,
    "errorShake": `errorShake-RkVI4HkN`,
    "settingsButton": `settingsButton-mrJVLRGb`,
    "shimmer": `shimmer-lCzub6k8`,
    "fadeInDelay2": `fadeInDelay2-ZracY-tQ`,
    "decorativeCircle2": `decorativeCircle2-t8HUdx07`,
    "phoneCard": `phoneCard-1wnvLyi2`,
    "fadeInDelay3": `fadeInDelay3-nbwSZP8A`,
    "fadeInDelay4": `fadeInDelay4-TJToLvzm`,
    "skeletonCard": `skeletonCard-fH7lshhh`,
    "statsTitle": `statsTitle-6Xh-RYy9`,
    "personnelCard": `personnelCard-N-N9u1us`,
    "alertCard": `alertCard-trqfzYSm`,
    "additionalInfo": `additionalInfo-SWj22vN7`,
    "personalInfoCard": `personalInfoCard-GMTUvuoL`,
    "onlineIndicator": `onlineIndicator-dZLd46X5`,
    "titleBar": `titleBar-GUpHpaO-`,
    "errorAnimation": `errorAnimation-SVfCef80`,
    "fadeInUp": `fadeInUp-LmQACtmh`,
    "vehicleCard": `vehicleCard-yHcN_T3F`,
    "float": `float-DAEgOouD`,
    "title": `title-OR5wN18v`,
    "pulse": `pulse-DmGLSG7j`,
    "fadeInDelay1": `fadeInDelay1-kMZ2-Q5v`,
    "emailCard": `emailCard-iN8WyD69`,
    "avatar": `avatar-_T1D7bf2`,
    "decorativeCircle1": `decorativeCircle1-HEEi2j9f`,
    "warningCard": `warningCard-eA6yGLZz`,
    "successAnimation": `successAnimation-UGiJBscn`,
    "avatarContainer": `avatarContainer-DSdWVJ9f`,
    "successPulse": `successPulse-xvCPq0dm`,
    "loadingContainer": `loadingContainer-lZKULeb8`
};

},
"src/pages/personal-center/PersonalInfo.tsx": function (module, exports, __mako_require__){
"use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_default = __mako_require__("@swc/helpers/_/_interop_require_default");
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
var _user = __mako_require__("src/services/user.ts");
var _UnifiedSettingsModal = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/personal-center/UnifiedSettingsModal.tsx"));
var _PersonalInfomodulecssasmodule = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/personal-center/PersonalInfo.module.css?asmodule"));
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
var _s = $RefreshSig$();
const { Title, Text } = _antd.Typography;
/**
 * 个人信息组件
 *
 * 显示用户的完整个人信息，包括基本信息和登录历史。
 * 整合了原UserProfileCard和LastLoginInfo组件的功能。
 *
 * 主要功能：
 * 1. 显示用户头像（使用姓名首字母）
 * 2. 显示用户姓名、邮箱、电话
 * 3. 显示注册日期
 * 4. 显示最后登录时间和登录团队
 *
 * 数据来源：
 * - 用户详细信息：通过UserService.getUserProfileDetail()获取
 */ const PersonalInfo = ()=>{
    var _userInfo_name;
    _s();
    /**
   * 用户详细信息状态管理
   */ const [userInfo, setUserInfo] = (0, _react.useState)({
        name: '',
        position: '',
        email: '',
        telephone: '',
        registerDate: '',
        lastLoginTime: '',
        lastLoginTeam: '',
        teamCount: 0,
        avatar: ''
    });
    const [userInfoLoading, setUserInfoLoading] = (0, _react.useState)(true);
    const [userInfoError, setUserInfoError] = (0, _react.useState)(null);
    // 数据概览状态管理
    const [personalStats, setPersonalStats] = (0, _react.useState)({
        vehicles: 0,
        personnel: 0,
        warnings: 0,
        alerts: 0
    });
    const [statsLoading, setStatsLoading] = (0, _react.useState)(true);
    const [statsError, setStatsError] = (0, _react.useState)(null);
    // Modal状态管理
    const [settingsModalVisible, setSettingsModalVisible] = (0, _react.useState)(false);
    // 获取用户数据和统计数据
    (0, _react.useEffect)(()=>{
        const fetchUserData = async ()=>{
            try {
                const userDetail = await _user.UserService.getUserProfileDetail();
                setUserInfo(userDetail);
                setUserInfoError(null);
            } catch (error) {
                console.error('获取用户详细信息失败:', error);
                setUserInfoError('获取用户详细信息失败，请稍后重试');
            } finally{
                setUserInfoLoading(false);
            }
        };
        const fetchStatsData = async ()=>{
            try {
                const stats = await _user.UserService.getUserPersonalStats();
                setPersonalStats(stats);
                setStatsError(null);
            } catch (error) {
                console.error('获取统计数据失败:', error);
                setStatsError('获取统计数据失败，请稍后重试');
            } finally{
                setStatsLoading(false);
            }
        };
        fetchUserData();
        fetchStatsData();
    }, []);
    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
        className: _PersonalInfomodulecssasmodule.default.personalInfoCard,
        styles: {
            body: {
                padding: 0
            }
        },
        children: [
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                className: _PersonalInfomodulecssasmodule.default.decorativeCircle1
            }, void 0, false, {
                fileName: "src/pages/personal-center/PersonalInfo.tsx",
                lineNumber: 122,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                className: _PersonalInfomodulecssasmodule.default.decorativeCircle2
            }, void 0, false, {
                fileName: "src/pages/personal-center/PersonalInfo.tsx",
                lineNumber: 123,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                className: `${_PersonalInfomodulecssasmodule.default.contentArea} ${_PersonalInfomodulecssasmodule.default.loadingContainer}`,
                children: [
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                        className: _PersonalInfomodulecssasmodule.default.titleBar,
                        children: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Typography.Title, {
                                level: 4,
                                className: _PersonalInfomodulecssasmodule.default.title,
                                children: "个人信息"
                            }, void 0, false, {
                                fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                lineNumber: 129,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                type: "text",
                                icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.SettingOutlined, {}, void 0, false, {
                                    fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                    lineNumber: 134,
                                    columnNumber: 19
                                }, void 0),
                                onClick: ()=>setSettingsModalVisible(true),
                                className: _PersonalInfomodulecssasmodule.default.settingsButton
                            }, void 0, false, {
                                fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                lineNumber: 132,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/personal-center/PersonalInfo.tsx",
                        lineNumber: 128,
                        columnNumber: 9
                    }, this),
                    userInfoError ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Alert, {
                        message: "个人信息加载失败",
                        description: userInfoError,
                        type: "error",
                        showIcon: true,
                        style: {
                            borderRadius: 12,
                            border: 'none'
                        }
                    }, void 0, false, {
                        fileName: "src/pages/personal-center/PersonalInfo.tsx",
                        lineNumber: 140,
                        columnNumber: 11
                    }, this) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Spin, {
                        spinning: userInfoLoading || statsLoading,
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                            gutter: [
                                32,
                                24
                            ],
                            align: "top",
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                    xs: 24,
                                    sm: 24,
                                    md: 14,
                                    lg: 14,
                                    xl: 14,
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                            style: {
                                                display: 'flex',
                                                alignItems: 'flex-start',
                                                gap: 20,
                                                marginBottom: 24
                                            },
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                    className: _PersonalInfomodulecssasmodule.default.avatarContainer,
                                                    children: [
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Avatar, {
                                                            size: 80,
                                                            className: _PersonalInfomodulecssasmodule.default.avatar,
                                                            icon: !userInfo.name && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {}, void 0, false, {
                                                                fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                lineNumber: 169,
                                                                columnNumber: 47
                                                            }, void 0),
                                                            children: (_userInfo_name = userInfo.name) === null || _userInfo_name === void 0 ? void 0 : _userInfo_name.charAt(0).toUpperCase()
                                                        }, void 0, false, {
                                                            fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                            lineNumber: 166,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                            className: _PersonalInfomodulecssasmodule.default.onlineIndicator
                                                        }, void 0, false, {
                                                            fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                            lineNumber: 174,
                                                            columnNumber: 21
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                    lineNumber: 165,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                    style: {
                                                        flex: 1,
                                                        minWidth: 0
                                                    },
                                                    children: [
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Typography.Title, {
                                                            level: 3,
                                                            style: {
                                                                margin: '0 0 8px 0',
                                                                fontSize: 24,
                                                                fontWeight: 600,
                                                                color: '#262626',
                                                                lineHeight: 1.2
                                                            },
                                                            children: userInfo.name || '加载中...'
                                                        }, void 0, false, {
                                                            fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                            lineNumber: 179,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                            direction: "vertical",
                                                            size: 12,
                                                            children: [
                                                                userInfo.email && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                    className: `${_PersonalInfomodulecssasmodule.default.contactCard} ${_PersonalInfomodulecssasmodule.default.emailCard}`,
                                                                    children: [
                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.MailOutlined, {
                                                                            style: {
                                                                                fontSize: 16,
                                                                                color: '#1890ff'
                                                                            }
                                                                        }, void 0, false, {
                                                                            fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                            lineNumber: 196,
                                                                            columnNumber: 27
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Typography.Text, {
                                                                            style: {
                                                                                color: '#595959',
                                                                                fontSize: 14,
                                                                                fontWeight: 500
                                                                            },
                                                                            children: userInfo.email
                                                                        }, void 0, false, {
                                                                            fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                            lineNumber: 202,
                                                                            columnNumber: 27
                                                                        }, this)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                    lineNumber: 195,
                                                                    columnNumber: 25
                                                                }, this),
                                                                userInfo.telephone && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                    className: `${_PersonalInfomodulecssasmodule.default.contactCard} ${_PersonalInfomodulecssasmodule.default.phoneCard}`,
                                                                    children: [
                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.PhoneOutlined, {
                                                                            style: {
                                                                                fontSize: 16,
                                                                                color: '#52c41a'
                                                                            }
                                                                        }, void 0, false, {
                                                                            fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                            lineNumber: 215,
                                                                            columnNumber: 27
                                                                        }, this),
                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Typography.Text, {
                                                                            style: {
                                                                                color: '#595959',
                                                                                fontSize: 14,
                                                                                fontWeight: 500
                                                                            },
                                                                            children: userInfo.telephone
                                                                        }, void 0, false, {
                                                                            fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                            lineNumber: 221,
                                                                            columnNumber: 27
                                                                        }, this)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                    lineNumber: 214,
                                                                    columnNumber: 25
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                            lineNumber: 193,
                                                            columnNumber: 21
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                    lineNumber: 178,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                            lineNumber: 156,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                            className: _PersonalInfomodulecssasmodule.default.additionalInfo,
                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                direction: "vertical",
                                                size: 8,
                                                style: {
                                                    width: '100%'
                                                },
                                                children: [
                                                    userInfo.registerDate && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                        style: {
                                                            display: 'flex',
                                                            alignItems: 'center',
                                                            gap: 8
                                                        },
                                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Typography.Text, {
                                                            style: {
                                                                fontSize: 13,
                                                                color: '#8c8c8c',
                                                                fontWeight: 500
                                                            },
                                                            children: [
                                                                "📅 注册于 ",
                                                                userInfo.registerDate
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                            lineNumber: 241,
                                                            columnNumber: 25
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                        lineNumber: 240,
                                                        columnNumber: 23
                                                    }, this),
                                                    userInfo.lastLoginTime && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                        style: {
                                                            display: 'flex',
                                                            alignItems: 'center',
                                                            gap: 8
                                                        },
                                                        children: [
                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.ClockCircleOutlined, {
                                                                style: {
                                                                    fontSize: 13,
                                                                    color: '#1890ff'
                                                                }
                                                            }, void 0, false, {
                                                                fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                lineNumber: 254,
                                                                columnNumber: 25
                                                            }, this),
                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Typography.Text, {
                                                                style: {
                                                                    fontSize: 13,
                                                                    color: '#8c8c8c',
                                                                    fontWeight: 500
                                                                },
                                                                children: [
                                                                    "最后登录：",
                                                                    userInfo.lastLoginTime
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                lineNumber: 260,
                                                                columnNumber: 25
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                        lineNumber: 253,
                                                        columnNumber: 23
                                                    }, this),
                                                    userInfo.lastLoginTeam && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                        style: {
                                                            display: 'flex',
                                                            alignItems: 'center',
                                                            gap: 8
                                                        },
                                                        children: [
                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.TeamOutlined, {
                                                                style: {
                                                                    fontSize: 13,
                                                                    color: '#52c41a'
                                                                }
                                                            }, void 0, false, {
                                                                fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                lineNumber: 273,
                                                                columnNumber: 25
                                                            }, this),
                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Typography.Text, {
                                                                style: {
                                                                    fontSize: 13,
                                                                    color: '#8c8c8c',
                                                                    fontWeight: 500
                                                                },
                                                                children: [
                                                                    "团队：",
                                                                    userInfo.lastLoginTeam
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                lineNumber: 279,
                                                                columnNumber: 25
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                        lineNumber: 272,
                                                        columnNumber: 23
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                lineNumber: 238,
                                                columnNumber: 19
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                            lineNumber: 237,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                    lineNumber: 155,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                    xs: 24,
                                    sm: 24,
                                    md: 10,
                                    lg: 10,
                                    xl: 10,
                                    children: statsError ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Alert, {
                                        message: "数据概览加载失败",
                                        description: statsError,
                                        type: "error",
                                        showIcon: true,
                                        style: {
                                            borderRadius: 12,
                                            border: 'none'
                                        }
                                    }, void 0, false, {
                                        fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                        lineNumber: 297,
                                        columnNumber: 19
                                    }, this) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                        children: [
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Typography.Title, {
                                                level: 5,
                                                className: _PersonalInfomodulecssasmodule.default.statsTitle,
                                                children: "数据概览"
                                            }, void 0, false, {
                                                fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                lineNumber: 309,
                                                columnNumber: 21
                                            }, this),
                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                                                gutter: [
                                                    12,
                                                    12
                                                ],
                                                children: [
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                                        xs: 12,
                                                        sm: 12,
                                                        md: 12,
                                                        lg: 12,
                                                        xl: 12,
                                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                                            size: "small",
                                                            className: `${_PersonalInfomodulecssasmodule.default.statsCard} ${_PersonalInfomodulecssasmodule.default.vehicleCard} ${_PersonalInfomodulecssasmodule.default.fadeInDelay1}`,
                                                            styles: {
                                                                body: {
                                                                    padding: '16px 12px',
                                                                    textAlign: 'center'
                                                                }
                                                            },
                                                            children: [
                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                    style: {
                                                                        marginBottom: 8
                                                                    },
                                                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CarOutlined, {
                                                                        style: {
                                                                            fontSize: 20,
                                                                            color: '#1890ff',
                                                                            marginBottom: 4
                                                                        }
                                                                    }, void 0, false, {
                                                                        fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                        lineNumber: 326,
                                                                        columnNumber: 29
                                                                    }, this)
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                    lineNumber: 325,
                                                                    columnNumber: 27
                                                                }, this),
                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                    style: {
                                                                        fontSize: 24,
                                                                        fontWeight: 700,
                                                                        color: '#1890ff',
                                                                        lineHeight: 1,
                                                                        marginBottom: 4
                                                                    },
                                                                    children: personalStats.vehicles
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                    lineNumber: 334,
                                                                    columnNumber: 27
                                                                }, this),
                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                    style: {
                                                                        fontSize: 12,
                                                                        color: '#1890ff',
                                                                        fontWeight: 600,
                                                                        opacity: 0.8
                                                                    },
                                                                    children: "车辆"
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                    lineNumber: 345,
                                                                    columnNumber: 27
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                            lineNumber: 315,
                                                            columnNumber: 25
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                        lineNumber: 314,
                                                        columnNumber: 23
                                                    }, this),
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                                        xs: 12,
                                                        sm: 12,
                                                        md: 12,
                                                        lg: 12,
                                                        xl: 12,
                                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                                            size: "small",
                                                            className: `${_PersonalInfomodulecssasmodule.default.statsCard} ${_PersonalInfomodulecssasmodule.default.personnelCard} ${_PersonalInfomodulecssasmodule.default.fadeInDelay2}`,
                                                            styles: {
                                                                body: {
                                                                    padding: '16px 12px',
                                                                    textAlign: 'center'
                                                                }
                                                            },
                                                            children: [
                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                    style: {
                                                                        marginBottom: 8
                                                                    },
                                                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UsergroupAddOutlined, {
                                                                        style: {
                                                                            fontSize: 20,
                                                                            color: '#52c41a',
                                                                            marginBottom: 4
                                                                        }
                                                                    }, void 0, false, {
                                                                        fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                        lineNumber: 371,
                                                                        columnNumber: 29
                                                                    }, this)
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                    lineNumber: 370,
                                                                    columnNumber: 27
                                                                }, this),
                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                    style: {
                                                                        fontSize: 24,
                                                                        fontWeight: 700,
                                                                        color: '#52c41a',
                                                                        lineHeight: 1,
                                                                        marginBottom: 4
                                                                    },
                                                                    children: personalStats.personnel
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                    lineNumber: 379,
                                                                    columnNumber: 27
                                                                }, this),
                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                    style: {
                                                                        fontSize: 12,
                                                                        color: '#52c41a',
                                                                        fontWeight: 600,
                                                                        opacity: 0.8
                                                                    },
                                                                    children: "人员"
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                    lineNumber: 390,
                                                                    columnNumber: 27
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                            lineNumber: 360,
                                                            columnNumber: 25
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                        lineNumber: 359,
                                                        columnNumber: 23
                                                    }, this),
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                                        xs: 12,
                                                        sm: 12,
                                                        md: 12,
                                                        lg: 12,
                                                        xl: 12,
                                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                                            size: "small",
                                                            className: `${_PersonalInfomodulecssasmodule.default.statsCard} ${_PersonalInfomodulecssasmodule.default.warningCard} ${_PersonalInfomodulecssasmodule.default.fadeInDelay3}`,
                                                            styles: {
                                                                body: {
                                                                    padding: '16px 12px',
                                                                    textAlign: 'center'
                                                                }
                                                            },
                                                            children: [
                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                    style: {
                                                                        marginBottom: 8
                                                                    },
                                                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.ExclamationCircleOutlined, {
                                                                        style: {
                                                                            fontSize: 20,
                                                                            color: '#faad14',
                                                                            marginBottom: 4
                                                                        }
                                                                    }, void 0, false, {
                                                                        fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                        lineNumber: 416,
                                                                        columnNumber: 29
                                                                    }, this)
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                    lineNumber: 415,
                                                                    columnNumber: 27
                                                                }, this),
                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                    style: {
                                                                        fontSize: 24,
                                                                        fontWeight: 700,
                                                                        color: '#faad14',
                                                                        lineHeight: 1,
                                                                        marginBottom: 4
                                                                    },
                                                                    children: personalStats.warnings
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                    lineNumber: 424,
                                                                    columnNumber: 27
                                                                }, this),
                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                    style: {
                                                                        fontSize: 12,
                                                                        color: '#faad14',
                                                                        fontWeight: 600,
                                                                        opacity: 0.8
                                                                    },
                                                                    children: "预警"
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                    lineNumber: 435,
                                                                    columnNumber: 27
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                            lineNumber: 405,
                                                            columnNumber: 25
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                        lineNumber: 404,
                                                        columnNumber: 23
                                                    }, this),
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                                        xs: 12,
                                                        sm: 12,
                                                        md: 12,
                                                        lg: 12,
                                                        xl: 12,
                                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                                            size: "small",
                                                            className: `${_PersonalInfomodulecssasmodule.default.statsCard} ${_PersonalInfomodulecssasmodule.default.alertCard} ${_PersonalInfomodulecssasmodule.default.fadeInDelay4}`,
                                                            styles: {
                                                                body: {
                                                                    padding: '16px 12px',
                                                                    textAlign: 'center'
                                                                }
                                                            },
                                                            children: [
                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                    style: {
                                                                        marginBottom: 8
                                                                    },
                                                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.AlertOutlined, {
                                                                        style: {
                                                                            fontSize: 20,
                                                                            color: '#ff4d4f',
                                                                            marginBottom: 4
                                                                        }
                                                                    }, void 0, false, {
                                                                        fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                        lineNumber: 461,
                                                                        columnNumber: 29
                                                                    }, this)
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                    lineNumber: 460,
                                                                    columnNumber: 27
                                                                }, this),
                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                    style: {
                                                                        fontSize: 24,
                                                                        fontWeight: 700,
                                                                        color: '#ff4d4f',
                                                                        lineHeight: 1,
                                                                        marginBottom: 4
                                                                    },
                                                                    children: personalStats.alerts
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                    lineNumber: 469,
                                                                    columnNumber: 27
                                                                }, this),
                                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                    style: {
                                                                        fontSize: 12,
                                                                        color: '#ff4d4f',
                                                                        fontWeight: 600,
                                                                        opacity: 0.8
                                                                    },
                                                                    children: "告警"
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                                    lineNumber: 480,
                                                                    columnNumber: 27
                                                                }, this)
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                            lineNumber: 450,
                                                            columnNumber: 25
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                        lineNumber: 449,
                                                        columnNumber: 23
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                                lineNumber: 312,
                                                columnNumber: 21
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                        lineNumber: 308,
                                        columnNumber: 19
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/personal-center/PersonalInfo.tsx",
                                    lineNumber: 295,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/personal-center/PersonalInfo.tsx",
                            lineNumber: 153,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "src/pages/personal-center/PersonalInfo.tsx",
                        lineNumber: 151,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "src/pages/personal-center/PersonalInfo.tsx",
                lineNumber: 126,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_UnifiedSettingsModal.default, {
                visible: settingsModalVisible,
                onCancel: ()=>setSettingsModalVisible(false),
                userInfo: userInfo,
                onSuccess: ()=>{
                    // 可以在这里刷新用户信息或团队列表
                    console.log('设置操作成功');
                }
            }, void 0, false, {
                fileName: "src/pages/personal-center/PersonalInfo.tsx",
                lineNumber: 502,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "src/pages/personal-center/PersonalInfo.tsx",
        lineNumber: 113,
        columnNumber: 5
    }, this);
};
_s(PersonalInfo, "IzjvzsvZ1ZoM5sGldeIs9cfa/44=");
_c = PersonalInfo;
var _default = PersonalInfo;
var _c;
$RefreshReg$(_c, "PersonalInfo");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
"src/pages/personal-center/TeamListCard.tsx": function (module, exports, __mako_require__){
"use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_default = __mako_require__("@swc/helpers/_/_interop_require_default");
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
var _max = __mako_require__("src/.umi/exports.ts");
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _procomponents = __mako_require__("node_modules/@ant-design/pro-components/es/index.js");
var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
var _services = __mako_require__("src/services/index.ts");
var _team = __mako_require__("src/services/team.ts");
var _tokenUtils = __mako_require__("src/utils/tokenUtils.ts");
var _teamSelectionUtils = __mako_require__("src/utils/teamSelectionUtils.ts");
var _TeamManagementModal = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/personal-center/components/TeamManagementModal.tsx"));
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
var _s = $RefreshSig$();
const { Text, Title } = _antd.Typography;
// 响应式布局样式
const styles = `
  .team-item .ant-pro-card-body {
    padding: 0 !important;
  }

  .team-item {
    margin-bottom: 24px !important; /* 增加团队间距 */
  }

  .team-item:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1) !important;
  }

  @media (max-width: 768px) {
    .team-item {
      margin-bottom: 20px !important; /* 中等屏幕保持较大间距 */
    }

    .team-stats-row {
      margin-top: 12px; /* 增加内部间距 */
    }

    .team-info-wrap {
      gap: 12px !important; /* 增加内部间距 */
    }
  }

  @media (max-width: 576px) {
    .team-item {
      margin-bottom: 16px !important; /* 小屏幕适当减少但仍保持较大间距 */
    }

    .team-stats-row {
      margin-top: 16px; /* 增加内部间距 */
    }

    .team-stats-col {
      margin-bottom: 8px; /* 增加内部间距 */
    }

    .team-info-wrap {
      gap: 10px !important; /* 增加内部间距 */
    }

    .team-meta-info {
      flex-wrap: wrap;
      gap: 10px !important; /* 增加内部间距 */
    }

    .team-status-badges {
      flex-wrap: wrap;
      gap: 8px !important; /* 增加内部间距 */
      margin-top: 8px; /* 增加内部间距 */
    }
  }

  @media (max-width: 480px) {
    .team-item {
      margin-bottom: 14px !important; /* 最小屏幕仍保持合理间距 */
    }

    .team-name-text {
      font-size: 16px !important; /* 增大字体 */
    }

    .team-meta-text {
      font-size: 13px !important; /* 增大字体 */
    }

    .team-meta-info {
      gap: 8px !important; /* 增加内部间距 */
    }

    .team-status-badges {
      gap: 6px !important; /* 增加内部间距 */
    }
  }
`;
/**
 * 团队列表卡片组件
 *
 * 这是个人中心页面的核心组件，负责显示用户所属的团队列表，
 * 并提供团队切换、创建团队等功能。是团队管理系统的重要入口。
 *
 * 主要功能：
 * 1. 显示用户所属的所有团队
 * 2. 支持团队切换功能
 * 3. 支持创建新团队
 * 4. 显示当前选择的团队状态
 * 5. 处理团队切换过程中的状态管理
 *
 * 状态管理：
 * - 团队列表数据的获取和显示
 * - 团队切换过程的加载状态
 * - 创建团队模态框的状态
 * - 错误状态的处理和显示
 *
 * 团队切换逻辑：
 * 1. 检查用户登录状态
 * 2. 判断是否为当前团队（避免重复切换）
 * 3. 调用后端API进行团队切换
 * 4. 更新本地Token和全局状态
 * 5. 跳转到团队仪表盘
 *
 * 与全局状态的集成：
 * - 监听用户登录状态变化
 * - 同步团队切换后的状态更新
 * - 处理用户注销时的状态清理
 */ const TeamListCard = ()=>{
    _s();
    /**
   * 团队列表相关状态管理
   *
   * 这些状态用于管理团队列表的显示和交互：
   * - teams: 用户所属的团队列表数据
   * - loading: 团队列表加载状态
   * - error: 错误信息（如网络错误、权限错误等）
   * - switchingTeamId: 当前正在切换的团队ID（用于显示加载状态）
   */ const [teams, setTeams] = (0, _react.useState)([]);
    const [loading, setLoading] = (0, _react.useState)(true);
    const [error, setError] = (0, _react.useState)(null);
    const [switchingTeamId, setSwitchingTeamId] = (0, _react.useState)(null);
    // 模态框状态管理
    const [teamManagementModalVisible, setTeamManagementModalVisible] = (0, _react.useState)(false);
    const [leaveTeamModalVisible, setLeaveTeamModalVisible] = (0, _react.useState)(false);
    const [selectedTeam, setSelectedTeam] = (0, _react.useState)(null);
    /**
   * 创建团队功能已移至设置页面
   *
   * 为了更好的用户体验和功能组织，创建团队功能已经移动到
   * 专门的设置页面中。用户可以通过"团队设置"按钮跳转到
   * 设置页面进行团队创建和管理操作。
   */ /**
   * 全局状态管理
   *
   * 从UmiJS全局状态中获取用户和团队信息：
   * - initialState: 包含当前用户和团队信息的全局状态
   * - setInitialState: 更新全局状态的函数
   * - currentTeam: 当前选择的团队信息
   */ const { initialState, setInitialState } = (0, _max.useModel)('@@initialState');
    const currentTeam = initialState === null || initialState === void 0 ? void 0 : initialState.currentTeam;
    /**
   * Token信息提取
   *
   * 从当前存储的Token中提取关键信息，用于状态判断和权限检查：
   * - currentTokenTeamId: Token中包含的团队ID
   * - currentUserId: Token中包含的用户ID
   * - hasTeamInToken: Token是否包含团队信息
   *
   * 这些信息用于：
   * - 判断当前是否已选择团队
   * - 确定哪个团队是当前激活的团队
   * - 记录用户的团队选择历史
   */ const currentTokenTeamId = (0, _tokenUtils.getTeamIdFromCurrentToken)();
    const currentUserId = (0, _tokenUtils.getUserIdFromCurrentToken)();
    const hasTeamInToken = (0, _tokenUtils.hasTeamInCurrentToken)();
    // 判断是否有真正的当前团队：
    // 1. Token中有团队信息（说明用户已经选择过团队）
    // 2. initialState中有团队信息（说明已经获取过团队详情）
    // 3. 两者的团队ID一致（确保状态同步）
    // 4. 用户曾经主动选择过这个团队（区分初始登录和主动选择）
    const hasRealCurrentTeam = !!(hasTeamInToken && currentTokenTeamId && currentTeam && currentTeam.id === currentTokenTeamId && currentUserId && (0, _teamSelectionUtils.hasUserSelectedTeam)(currentUserId, currentTokenTeamId));
    // 获取实际的当前团队ID：只有在用户真正选择过团队且状态同步时才返回团队ID
    const actualCurrentTeamId = hasRealCurrentTeam ? currentTokenTeamId : null;
    // 调试日志
    console.log('TeamListCard 状态调试:', {
        currentTeam: currentTeam === null || currentTeam === void 0 ? void 0 : currentTeam.id,
        currentTokenTeamId,
        currentUserId,
        hasTeamInToken,
        hasRealCurrentTeam,
        actualCurrentTeamId,
        hasUserSelectedCurrentTeam: currentUserId && currentTokenTeamId ? (0, _teamSelectionUtils.hasUserSelectedTeam)(currentUserId, currentTokenTeamId) : false,
        initialStateCurrentUser: !!(initialState === null || initialState === void 0 ? void 0 : initialState.currentUser)
    });
    // 获取团队列表数据
    const fetchTeams = async ()=>{
        try {
            setLoading(true);
            setError(null);
            const teamsData = await _team.TeamService.getUserTeamsWithStats();
            setTeams(teamsData);
        } catch (error) {
            console.error('获取团队列表失败:', error);
            setError('获取团队列表失败');
        } finally{
            setLoading(false);
        }
    };
    (0, _react.useEffect)(()=>{
        // 只有在用户已登录时才获取团队列表
        if (initialState === null || initialState === void 0 ? void 0 : initialState.currentUser) fetchTeams();
    }, [
        initialState === null || initialState === void 0 ? void 0 : initialState.currentUser
    ]);
    // 监听全局状态变化，处理注销等情况
    (0, _react.useEffect)(()=>{
        // 如果用户已注销（currentUser为undefined），清除本地团队列表状态
        if (!(initialState === null || initialState === void 0 ? void 0 : initialState.currentUser)) {
            setTeams([]);
            setError(null);
            setLoading(false);
            setSwitchingTeamId(null);
        }
    }, [
        initialState === null || initialState === void 0 ? void 0 : initialState.currentUser
    ]);
    // 监听当前团队状态变化
    (0, _react.useEffect)(()=>{
        console.log('当前团队状态变化:', {
            currentTeam: currentTeam === null || currentTeam === void 0 ? void 0 : currentTeam.id,
            actualCurrentTeamId,
            hasRealCurrentTeam
        });
    }, [
        currentTeam === null || currentTeam === void 0 ? void 0 : currentTeam.id,
        actualCurrentTeamId,
        hasRealCurrentTeam
    ]);
    // 创建团队功能已移至设置页面，此处不再需要处理函数
    /**
   * 团队切换处理函数
   *
   * 这是团队切换功能的核心函数，处理用户从一个团队切换到另一个团队的完整流程。
   * 包括权限检查、API调用、状态更新、页面跳转等步骤。
   *
   * 切换流程：
   * 1. 用户登录状态检查
   * 2. 当前团队状态判断（避免重复切换）
   * 3. 调用后端团队选择API
   * 4. 验证切换结果
   * 5. 更新本地Token和全局状态
   * 6. 记录用户选择历史
   * 7. 跳转到团队仪表盘
   *
   * 状态管理：
   * - 设置切换加载状态（防止重复点击）
   * - 更新全局用户和团队状态
   * - 处理切换过程中的错误状态
   *
   * 错误处理：
   * - 网络错误：显示网络连接提示
   * - 权限错误：由响应拦截器统一处理
   * - 业务错误：显示具体的错误信息
   *
   * @param teamId 要切换到的团队ID
   * @param teamName 团队名称（用于显示消息）
   */ const handleTeamSwitch = async (teamId, teamName)=>{
        /**
     * 用户登录状态检查
     *
     * 确保用户已登录才能进行团队切换操作。
     * 虽然组件层面已有登录检查，但这里再次确认以确保安全性。
     */ if (!(initialState === null || initialState === void 0 ? void 0 : initialState.currentUser)) return;
        try {
            /**
       * 设置切换状态
       *
       * 标记当前正在切换的团队ID，用于：
       * 1. 在UI上显示加载状态
       * 2. 防止用户重复点击
       * 3. 提供视觉反馈
       */ setSwitchingTeamId(teamId);
            /**
       * 当前团队检查
       *
       * 如果用户点击的是当前已选择的团队，直接跳转到仪表盘，
       * 避免不必要的API调用和Token更新。
       */ if (teamId === actualCurrentTeamId) {
                _max.history.push('/dashboard');
                return;
            }
            /**
       * 执行团队切换API调用
       *
       * 调用后端的团队选择接口，后端会：
       * 1. 验证用户是否有权限访问该团队
       * 2. 生成包含新团队信息的JWT Token
       * 3. 返回团队详细信息和切换状态
       */ const response = await _services.AuthService.selectTeam({
                teamId
            });
            /**
       * 验证切换结果
       *
       * 检查后端返回的响应是否表示切换成功：
       * - teamSelectionSuccess: 切换成功标识
       * - team: 新团队的详细信息
       * - team.id: 确认返回的团队ID与请求的一致
       */ if (response.teamSelectionSuccess && response.team && response.team.id === teamId) {
                /**
         * 记录用户选择历史
         *
         * 将用户的团队选择记录到本地存储，用于：
         * - 下次登录时的默认团队选择
         * - 用户行为分析
         * - 提升用户体验
         */ if (currentUserId) (0, _teamSelectionUtils.recordTeamSelection)(currentUserId, teamId);
                /**
         * 异步更新全局状态
         *
         * 由于Token已经更新，需要同步更新全局状态中的用户和团队信息。
         * 使用异步更新避免阻塞页面跳转，提升用户体验。
         *
         * 更新流程：
         * 1. 并行获取最新的用户信息和团队信息
         * 2. 验证获取的团队信息是否正确
         * 3. 更新全局状态
         * 4. 处理更新过程中的错误
         */ if ((initialState === null || initialState === void 0 ? void 0 : initialState.fetchTeamInfo) && (initialState === null || initialState === void 0 ? void 0 : initialState.fetchUserInfo) && setInitialState) // 异步更新状态，不阻塞跳转
                Promise.all([
                    initialState.fetchUserInfo(),
                    initialState.fetchTeamInfo()
                ]).then(([currentUser, currentTeam])=>{
                    // 确认获取的团队信息与切换的团队一致
                    if (currentTeam && currentTeam.id === teamId) setInitialState({
                        ...initialState,
                        currentUser,
                        currentTeam
                    });
                }).catch((error)=>{
                    console.error('更新 initialState 失败:', error);
                // 状态更新失败不影响团队切换的核心功能
                });
                /**
         * 页面跳转
         *
         * 切换成功后跳转到团队仪表盘。
         * 路由守卫会验证新的Token并允许访问团队页面。
         */ _max.history.push('/dashboard');
            }
        } catch (error) {
        /**
       * 异常处理
       *
       * 处理团队切换过程中可能出现的各种异常：
       * - 网络错误：连接超时、服务器不可达等
       * - 权限错误：用户无权限访问该团队
       * - 业务错误：团队不存在、状态异常等
       *
       * 错误处理策略：
       * 1. 记录详细的错误日志用于调试
       * 2. 响应拦截器已处理大部分错误消息
       * 3. 只对网络错误显示通用提示
       */ // 错误处理由响应拦截器统一处理
        } finally{
            /**
       * 清理切换状态
       *
       * 无论切换成功还是失败，都要清除切换状态，
       * 恢复UI的正常状态，允许用户进行下一次操作。
       */ setSwitchingTeamId(null);
        }
    };
    /**
   * 处理团队管理
   */ const handleTeamManagement = async (team)=>{
        try {
            // 先切换到目标团队以确保有正确的权限
            await _services.AuthService.selectTeam({
                teamId: team.id
            });
            setSelectedTeam(team);
            setTeamManagementModalVisible(true);
        } catch (error) {
            console.error('切换团队失败:', error);
            _antd.message.error('无法打开团队管理');
        }
    };
    /**
   * 处理退出团队
   */ const handleLeaveTeam = (team)=>{
        setSelectedTeam(team);
        setLeaveTeamModalVisible(true);
    };
    /**
   * 确认退出团队
   */ const confirmLeaveTeam = async ()=>{
        if (!selectedTeam) return;
        try {
            // 先切换到目标团队
            await _services.AuthService.selectTeam({
                teamId: selectedTeam.id
            });
            // 退出团队
            await _team.TeamService.leaveTeam();
            // 重新获取团队列表
            await fetchTeams();
            // 更新全局状态，清除当前团队
            if (setInitialState) await setInitialState((prevState)=>({
                    ...prevState,
                    currentTeam: undefined
                }));
            _antd.message.success('已成功退出团队');
            setLeaveTeamModalVisible(false);
            setSelectedTeam(null);
        } catch (error) {
            console.error('退出团队失败:', error);
            _antd.message.error('退出团队失败');
        }
    };
    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_jsxdevruntime.Fragment, {
        children: [
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("style", {
                dangerouslySetInnerHTML: {
                    __html: styles
                }
            }, void 0, false, {
                fileName: "src/pages/personal-center/TeamListCard.tsx",
                lineNumber: 523,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.ProCard, {
                title: "团队列表",
                style: {
                    borderRadius: 8,
                    marginBottom: 16
                },
                headStyle: {
                    borderBottom: '1px solid #f0f0f0',
                    paddingBottom: 12
                },
                bodyStyle: {
                    padding: '16px'
                },
                children: error ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Alert, {
                    message: "团队列表加载失败",
                    description: error,
                    type: "error",
                    showIcon: true,
                    style: {
                        marginBottom: 16
                    }
                }, void 0, false, {
                    fileName: "src/pages/personal-center/TeamListCard.tsx",
                    lineNumber: 540,
                    columnNumber: 11
                }, this) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Spin, {
                    spinning: loading,
                    children: !(initialState === null || initialState === void 0 ? void 0 : initialState.currentUser) ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                        style: {
                            textAlign: 'center',
                            padding: '40px 20px'
                        },
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                            type: "secondary",
                            children: "请先登录以查看团队列表"
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/TeamListCard.tsx",
                            lineNumber: 551,
                            columnNumber: 17
                        }, this)
                    }, void 0, false, {
                        fileName: "src/pages/personal-center/TeamListCard.tsx",
                        lineNumber: 550,
                        columnNumber: 15
                    }, this) : teams.length === 0 && !loading ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                        style: {
                            textAlign: 'center',
                            padding: '40px 20px'
                        },
                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                            type: "secondary",
                            children: "暂无团队，请先加入或创建团队"
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/TeamListCard.tsx",
                            lineNumber: 555,
                            columnNumber: 17
                        }, this)
                    }, void 0, false, {
                        fileName: "src/pages/personal-center/TeamListCard.tsx",
                        lineNumber: 554,
                        columnNumber: 15
                    }, this) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.ProList, {
                        dataSource: teams,
                        split: false,
                        itemLayout: "vertical" /* 垂直布局 */ ,
                        renderItem: (item)=>{
                            var _item_stats, _item_stats1, _item_stats2, _item_stats3;
                            return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.ProCard, {
                                className: "team-item",
                                style: {
                                    background: actualCurrentTeamId === item.id ? 'linear-gradient(135deg, #f0f9ff, #e6f4ff)' : '#fff',
                                    borderRadius: 8,
                                    boxShadow: actualCurrentTeamId === item.id ? '0 2px 8px rgba(24, 144, 255, 0.12)' : '0 1px 4px rgba(0,0,0,0.06)',
                                    width: '100%',
                                    borderLeft: `3px solid ${item.isCreator ? '#722ed1' : '#52c41a'}`,
                                    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                                    border: actualCurrentTeamId === item.id ? '1px solid #91caff' : '1px solid #f0f0f0',
                                    padding: '20px 24px',
                                    /* 增加内边距 */ position: 'relative',
                                    overflow: 'hidden'
                                },
                                hoverable: true,
                                onMouseEnter: (e)=>{
                                    if (actualCurrentTeamId !== item.id) {
                                        e.currentTarget.style.transform = 'translateY(-2px)';
                                        e.currentTarget.style.boxShadow = '0 8px 24px rgba(0,0,0,0.12)';
                                    }
                                },
                                onMouseLeave: (e)=>{
                                    if (actualCurrentTeamId !== item.id) {
                                        e.currentTarget.style.transform = 'translateY(0)';
                                        e.currentTarget.style.boxShadow = '0 2px 8px rgba(0,0,0,0.06)';
                                    }
                                },
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                                    gutter: [
                                        8,
                                        8
                                    ],
                                    align: "middle",
                                    style: {
                                        width: '100%'
                                    },
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                            xs: 24,
                                            sm: 24,
                                            md: 14,
                                            lg: 12,
                                            xl: 14,
                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                vertical: true,
                                                gap: 12,
                                                className: "team-info-wrap",
                                                children: [
                                                    " ",
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                        align: "center",
                                                        gap: 12,
                                                        wrap: "wrap",
                                                        children: [
                                                            " ",
                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                                style: {
                                                                    cursor: 'pointer',
                                                                    padding: '2px 4px',
                                                                    borderRadius: 4,
                                                                    transition: 'all 0.2s ease',
                                                                    display: 'flex',
                                                                    alignItems: 'center',
                                                                    gap: 6
                                                                },
                                                                onClick: ()=>handleTeamSwitch(item.id, item.name),
                                                                onMouseEnter: (e)=>{
                                                                    e.currentTarget.style.background = 'rgba(24, 144, 255, 0.05)';
                                                                },
                                                                onMouseLeave: (e)=>{
                                                                    e.currentTarget.style.background = 'transparent';
                                                                },
                                                                children: [
                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                        strong: true,
                                                                        style: {
                                                                            fontSize: 20,
                                                                            /* 增大团队名称字体 */ color: actualCurrentTeamId === item.id ? '#1890ff' : '#262626',
                                                                            lineHeight: 1.3
                                                                        },
                                                                        children: item.name
                                                                    }, void 0, false, {
                                                                        fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                        lineNumber: 635,
                                                                        columnNumber: 33
                                                                    }, void 0),
                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.RightOutlined, {
                                                                        style: {
                                                                            fontSize: 12,
                                                                            /* 增大图标大小 */ color: actualCurrentTeamId === item.id ? '#1890ff' : '#8c8c8c',
                                                                            verticalAlign: 'middle',
                                                                            display: 'inline-flex',
                                                                            alignItems: 'center'
                                                                        }
                                                                    }, void 0, false, {
                                                                        fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                        lineNumber: 648,
                                                                        columnNumber: 33
                                                                    }, void 0)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                lineNumber: 613,
                                                                columnNumber: 31
                                                            }, void 0),
                                                            actualCurrentTeamId === item.id && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("span", {
                                                                style: {
                                                                    background: '#1890ff',
                                                                    color: 'white',
                                                                    padding: '3px 8px',
                                                                    /* 增加内边距 */ borderRadius: 8,
                                                                    fontSize: 12,
                                                                    /* 增大字体 */ fontWeight: 500
                                                                },
                                                                children: "当前"
                                                            }, void 0, false, {
                                                                fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                lineNumber: 664,
                                                                columnNumber: 33
                                                            }, void 0),
                                                            switchingTeamId === item.id && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                                align: "center",
                                                                gap: 4,
                                                                children: [
                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Spin, {
                                                                        size: "small"
                                                                    }, void 0, false, {
                                                                        fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                        lineNumber: 682,
                                                                        columnNumber: 35
                                                                    }, void 0),
                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                        style: {
                                                                            fontSize: 12,
                                                                            color: '#666'
                                                                        },
                                                                        children: [
                                                                            " ",
                                                                            "切换中"
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                        lineNumber: 683,
                                                                        columnNumber: 35
                                                                    }, void 0)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                lineNumber: 681,
                                                                columnNumber: 33
                                                            }, void 0)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                        lineNumber: 612,
                                                        columnNumber: 29
                                                    }, void 0),
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                        align: "center",
                                                        gap: 16,
                                                        wrap: "wrap",
                                                        className: "team-meta-info",
                                                        children: [
                                                            " ",
                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tooltip, {
                                                                title: `团队创建时间: ${new Date(item.createdAt).toLocaleString('zh-CN')}`,
                                                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                                    align: "center",
                                                                    gap: 6,
                                                                    children: [
                                                                        " ",
                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.ClockCircleOutlined, {
                                                                            style: {
                                                                                color: '#8c8c8c',
                                                                                fontSize: 14
                                                                            }
                                                                        }, void 0, false, {
                                                                            fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                            lineNumber: 696,
                                                                            columnNumber: 35
                                                                        }, void 0),
                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                            style: {
                                                                                fontSize: 14,
                                                                                color: '#8c8c8c'
                                                                            },
                                                                            children: [
                                                                                "创建: ",
                                                                                new Date(item.createdAt).toLocaleDateString('zh-CN')
                                                                            ]
                                                                        }, void 0, true, {
                                                                            fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                            lineNumber: 699,
                                                                            columnNumber: 35
                                                                        }, void 0)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                    lineNumber: 695,
                                                                    columnNumber: 33
                                                                }, void 0)
                                                            }, void 0, false, {
                                                                fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                lineNumber: 692,
                                                                columnNumber: 31
                                                            }, void 0),
                                                            item.assignedAt && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tooltip, {
                                                                title: `加入团队时间: ${new Date(item.assignedAt).toLocaleString('zh-CN')}`,
                                                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                                    align: "center",
                                                                    gap: 6,
                                                                    children: [
                                                                        " ",
                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {
                                                                            style: {
                                                                                color: '#8c8c8c',
                                                                                fontSize: 14
                                                                            }
                                                                        }, void 0, false, {
                                                                            fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                            lineNumber: 715,
                                                                            columnNumber: 37
                                                                        }, void 0),
                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                            style: {
                                                                                fontSize: 14,
                                                                                color: '#8c8c8c'
                                                                            },
                                                                            children: [
                                                                                "加入: ",
                                                                                new Date(item.assignedAt).toLocaleDateString('zh-CN')
                                                                            ]
                                                                        }, void 0, true, {
                                                                            fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                            lineNumber: 718,
                                                                            columnNumber: 37
                                                                        }, void 0)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                    lineNumber: 714,
                                                                    columnNumber: 35
                                                                }, void 0)
                                                            }, void 0, false, {
                                                                fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                lineNumber: 711,
                                                                columnNumber: 33
                                                            }, void 0),
                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tooltip, {
                                                                title: `团队成员: ${item.memberCount}人`,
                                                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                                    align: "center",
                                                                    gap: 6,
                                                                    children: [
                                                                        " ",
                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.TeamOutlined, {
                                                                            style: {
                                                                                color: '#8c8c8c',
                                                                                fontSize: 14
                                                                            }
                                                                        }, void 0, false, {
                                                                            fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                            lineNumber: 733,
                                                                            columnNumber: 35
                                                                        }, void 0),
                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                            style: {
                                                                                fontSize: 14,
                                                                                color: '#8c8c8c'
                                                                            },
                                                                            children: [
                                                                                item.memberCount,
                                                                                " 人"
                                                                            ]
                                                                        }, void 0, true, {
                                                                            fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                            lineNumber: 736,
                                                                            columnNumber: 35
                                                                        }, void 0)
                                                                    ]
                                                                }, void 0, true, {
                                                                    fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                    lineNumber: 732,
                                                                    columnNumber: 33
                                                                }, void 0)
                                                            }, void 0, false, {
                                                                fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                lineNumber: 729,
                                                                columnNumber: 31
                                                            }, void 0)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                        lineNumber: 691,
                                                        columnNumber: 29
                                                    }, void 0),
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                        align: "center",
                                                        gap: 12,
                                                        wrap: "wrap",
                                                        className: "team-status-badges",
                                                        children: [
                                                            " ",
                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("span", {
                                                                style: {
                                                                    background: item.isCreator ? '#722ed1' : '#52c41a',
                                                                    color: 'white',
                                                                    padding: '4px 8px',
                                                                    /* 增加内边距 */ borderRadius: 8,
                                                                    fontSize: 12,
                                                                    /* 增大字体 */ fontWeight: 500,
                                                                    display: 'flex',
                                                                    alignItems: 'center',
                                                                    gap: 2
                                                                },
                                                                children: item.isCreator ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_jsxdevruntime.Fragment, {
                                                                    children: [
                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CrownOutlined, {
                                                                            style: {
                                                                                fontSize: 11
                                                                            }
                                                                        }, void 0, false, {
                                                                            fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                            lineNumber: 765,
                                                                            columnNumber: 37
                                                                        }, void 0),
                                                                        " ",
                                                                        "管理员"
                                                                    ]
                                                                }, void 0, true) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_jsxdevruntime.Fragment, {
                                                                    children: [
                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {
                                                                            style: {
                                                                                fontSize: 11
                                                                            }
                                                                        }, void 0, false, {
                                                                            fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                            lineNumber: 770,
                                                                            columnNumber: 37
                                                                        }, void 0),
                                                                        " ",
                                                                        "成员"
                                                                    ]
                                                                }, void 0, true)
                                                            }, void 0, false, {
                                                                fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                lineNumber: 748,
                                                                columnNumber: 31
                                                            }, void 0),
                                                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("span", {
                                                                style: {
                                                                    background: item.isActive ? '#52c41a' : '#ff4d4f',
                                                                    color: 'white',
                                                                    padding: '4px 8px',
                                                                    /* 增加内边距 */ borderRadius: 8,
                                                                    fontSize: 12,
                                                                    /* 增大字体 */ fontWeight: 500,
                                                                    display: 'flex',
                                                                    alignItems: 'center',
                                                                    gap: 2
                                                                },
                                                                children: item.isActive ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_jsxdevruntime.Fragment, {
                                                                    children: [
                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CheckCircleOutlined, {
                                                                            style: {
                                                                                fontSize: 11
                                                                            }
                                                                        }, void 0, false, {
                                                                            fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                            lineNumber: 792,
                                                                            columnNumber: 37
                                                                        }, void 0),
                                                                        " ",
                                                                        "启用"
                                                                    ]
                                                                }, void 0, true) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_jsxdevruntime.Fragment, {
                                                                    children: [
                                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.MinusCircleOutlined, {
                                                                            style: {
                                                                                fontSize: 11
                                                                            }
                                                                        }, void 0, false, {
                                                                            fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                            lineNumber: 797,
                                                                            columnNumber: 37
                                                                        }, void 0),
                                                                        " ",
                                                                        "停用"
                                                                    ]
                                                                }, void 0, true)
                                                            }, void 0, false, {
                                                                fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                lineNumber: 777,
                                                                columnNumber: 31
                                                            }, void 0),
                                                            item.isCreator ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tooltip, {
                                                                title: "团队管理",
                                                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                                                    type: "text",
                                                                    size: "small",
                                                                    icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.SettingOutlined, {
                                                                        style: {
                                                                            fontSize: 12
                                                                        }
                                                                    }, void 0, false, {
                                                                        fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                        lineNumber: 809,
                                                                        columnNumber: 43
                                                                    }, void 0),
                                                                    onClick: (e)=>{
                                                                        e.stopPropagation();
                                                                        handleTeamManagement(item);
                                                                    },
                                                                    style: {
                                                                        color: '#722ed1',
                                                                        display: 'flex',
                                                                        alignItems: 'center',
                                                                        justifyContent: 'center',
                                                                        width: 24,
                                                                        height: 24,
                                                                        padding: 0,
                                                                        borderRadius: 4,
                                                                        border: '1px solid #d3adf7',
                                                                        background: 'rgba(114, 46, 209, 0.04)',
                                                                        transition: 'all 0.2s ease'
                                                                    },
                                                                    onMouseEnter: (e)=>{
                                                                        e.currentTarget.style.background = 'rgba(114, 46, 209, 0.1)';
                                                                        e.currentTarget.style.borderColor = '#722ed1';
                                                                    },
                                                                    onMouseLeave: (e)=>{
                                                                        e.currentTarget.style.background = 'rgba(114, 46, 209, 0.04)';
                                                                        e.currentTarget.style.borderColor = '#d3adf7';
                                                                    }
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                    lineNumber: 806,
                                                                    columnNumber: 35
                                                                }, void 0)
                                                            }, void 0, false, {
                                                                fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                lineNumber: 805,
                                                                columnNumber: 33
                                                            }, void 0) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tooltip, {
                                                                title: "退出团队",
                                                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                                                    type: "text",
                                                                    size: "small",
                                                                    icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.LogoutOutlined, {
                                                                        style: {
                                                                            fontSize: 12
                                                                        }
                                                                    }, void 0, false, {
                                                                        fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                        lineNumber: 842,
                                                                        columnNumber: 43
                                                                    }, void 0),
                                                                    onClick: (e)=>{
                                                                        e.stopPropagation();
                                                                        handleLeaveTeam(item);
                                                                    },
                                                                    style: {
                                                                        color: '#ff4d4f',
                                                                        display: 'flex',
                                                                        alignItems: 'center',
                                                                        justifyContent: 'center',
                                                                        width: 24,
                                                                        height: 24,
                                                                        padding: 0,
                                                                        borderRadius: 4,
                                                                        border: '1px solid #ffccc7',
                                                                        background: 'rgba(255, 77, 79, 0.04)',
                                                                        transition: 'all 0.2s ease'
                                                                    },
                                                                    onMouseEnter: (e)=>{
                                                                        e.currentTarget.style.background = 'rgba(255, 77, 79, 0.1)';
                                                                        e.currentTarget.style.borderColor = '#ff4d4f';
                                                                    },
                                                                    onMouseLeave: (e)=>{
                                                                        e.currentTarget.style.background = 'rgba(255, 77, 79, 0.04)';
                                                                        e.currentTarget.style.borderColor = '#ffccc7';
                                                                    }
                                                                }, void 0, false, {
                                                                    fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                    lineNumber: 839,
                                                                    columnNumber: 35
                                                                }, void 0)
                                                            }, void 0, false, {
                                                                fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                lineNumber: 838,
                                                                columnNumber: 33
                                                            }, void 0)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                        lineNumber: 746,
                                                        columnNumber: 29
                                                    }, void 0)
                                                ]
                                            }, void 0, true, {
                                                fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                lineNumber: 610,
                                                columnNumber: 27
                                            }, void 0)
                                        }, void 0, false, {
                                            fileName: "src/pages/personal-center/TeamListCard.tsx",
                                            lineNumber: 609,
                                            columnNumber: 25
                                        }, void 0),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                            xs: 24,
                                            sm: 24,
                                            md: 10,
                                            lg: 12,
                                            xl: 10,
                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                                                gutter: [
                                                    8,
                                                    8
                                                ],
                                                justify: {
                                                    xs: 'start',
                                                    md: 'end'
                                                },
                                                align: "middle",
                                                children: [
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                                        xs: 6,
                                                        sm: 6,
                                                        md: 6,
                                                        lg: 6,
                                                        xl: 6,
                                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                            style: {
                                                                background: '#f0f7ff',
                                                                border: '1px solid #d9e8ff',
                                                                borderRadius: 6,
                                                                padding: '8px 10px',
                                                                /* 增加内边距 */ textAlign: 'center',
                                                                minWidth: '55px'
                                                            },
                                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                                vertical: true,
                                                                align: "center",
                                                                gap: 3,
                                                                children: [
                                                                    " ",
                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CarOutlined, {
                                                                        style: {
                                                                            color: '#1890ff',
                                                                            fontSize: 16
                                                                        }
                                                                    }, void 0, false, {
                                                                        fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                        lineNumber: 895,
                                                                        columnNumber: 35
                                                                    }, void 0),
                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                        strong: true,
                                                                        style: {
                                                                            fontSize: 18,
                                                                            /* 增大数字字体 */ color: '#1890ff',
                                                                            lineHeight: 1.2
                                                                        },
                                                                        children: ((_item_stats = item.stats) === null || _item_stats === void 0 ? void 0 : _item_stats.vehicles) || 0
                                                                    }, void 0, false, {
                                                                        fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                        lineNumber: 898,
                                                                        columnNumber: 35
                                                                    }, void 0),
                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                        style: {
                                                                            fontSize: 11,
                                                                            color: '#666'
                                                                        },
                                                                        children: [
                                                                            " ",
                                                                            "车辆"
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                        lineNumber: 908,
                                                                        columnNumber: 35
                                                                    }, void 0)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                lineNumber: 894,
                                                                columnNumber: 33
                                                            }, void 0)
                                                        }, void 0, false, {
                                                            fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                            lineNumber: 884,
                                                            columnNumber: 31
                                                        }, void 0)
                                                    }, void 0, false, {
                                                        fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                        lineNumber: 883,
                                                        columnNumber: 29
                                                    }, void 0),
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                                        xs: 6,
                                                        sm: 6,
                                                        md: 6,
                                                        lg: 6,
                                                        xl: 6,
                                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                            style: {
                                                                background: '#f6ffed',
                                                                border: '1px solid #d1f0be',
                                                                borderRadius: 6,
                                                                padding: '8px 10px',
                                                                /* 增加内边距 */ textAlign: 'center',
                                                                minWidth: '55px'
                                                            },
                                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                                vertical: true,
                                                                align: "center",
                                                                gap: 3,
                                                                children: [
                                                                    " ",
                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {
                                                                        style: {
                                                                            color: '#52c41a',
                                                                            fontSize: 16
                                                                        }
                                                                    }, void 0, false, {
                                                                        fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                        lineNumber: 928,
                                                                        columnNumber: 35
                                                                    }, void 0),
                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                        strong: true,
                                                                        style: {
                                                                            fontSize: 18,
                                                                            /* 增大数字字体 */ color: '#52c41a',
                                                                            lineHeight: 1.2
                                                                        },
                                                                        children: ((_item_stats1 = item.stats) === null || _item_stats1 === void 0 ? void 0 : _item_stats1.personnel) || 0
                                                                    }, void 0, false, {
                                                                        fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                        lineNumber: 931,
                                                                        columnNumber: 35
                                                                    }, void 0),
                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                        style: {
                                                                            fontSize: 11,
                                                                            color: '#666'
                                                                        },
                                                                        children: [
                                                                            " ",
                                                                            "人员"
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                        lineNumber: 941,
                                                                        columnNumber: 35
                                                                    }, void 0)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                lineNumber: 927,
                                                                columnNumber: 33
                                                            }, void 0)
                                                        }, void 0, false, {
                                                            fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                            lineNumber: 917,
                                                            columnNumber: 31
                                                        }, void 0)
                                                    }, void 0, false, {
                                                        fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                        lineNumber: 916,
                                                        columnNumber: 29
                                                    }, void 0),
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                                        xs: 6,
                                                        sm: 6,
                                                        md: 6,
                                                        lg: 6,
                                                        xl: 6,
                                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                            style: {
                                                                background: '#fff7e6',
                                                                border: '1px solid #ffd666',
                                                                borderRadius: 6,
                                                                padding: '8px 10px',
                                                                /* 增加内边距 */ textAlign: 'center',
                                                                minWidth: '55px'
                                                            },
                                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                                vertical: true,
                                                                align: "center",
                                                                gap: 3,
                                                                children: [
                                                                    " ",
                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.ExclamationCircleOutlined, {
                                                                        style: {
                                                                            color: '#faad14',
                                                                            fontSize: 16
                                                                        }
                                                                    }, void 0, false, {
                                                                        fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                        lineNumber: 961,
                                                                        columnNumber: 35
                                                                    }, void 0),
                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                        strong: true,
                                                                        style: {
                                                                            fontSize: 18,
                                                                            /* 增大数字字体 */ color: '#faad14',
                                                                            lineHeight: 1.2
                                                                        },
                                                                        children: ((_item_stats2 = item.stats) === null || _item_stats2 === void 0 ? void 0 : _item_stats2.expiring) || 0
                                                                    }, void 0, false, {
                                                                        fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                        lineNumber: 964,
                                                                        columnNumber: 35
                                                                    }, void 0),
                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                        style: {
                                                                            fontSize: 11,
                                                                            color: '#666'
                                                                        },
                                                                        children: [
                                                                            " ",
                                                                            "临期"
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                        lineNumber: 974,
                                                                        columnNumber: 35
                                                                    }, void 0)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                lineNumber: 960,
                                                                columnNumber: 33
                                                            }, void 0)
                                                        }, void 0, false, {
                                                            fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                            lineNumber: 950,
                                                            columnNumber: 31
                                                        }, void 0)
                                                    }, void 0, false, {
                                                        fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                        lineNumber: 949,
                                                        columnNumber: 29
                                                    }, void 0),
                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                                        xs: 6,
                                                        sm: 6,
                                                        md: 6,
                                                        lg: 6,
                                                        xl: 6,
                                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                            style: {
                                                                background: '#fff1f0',
                                                                border: '1px solid #ffccc7',
                                                                borderRadius: 6,
                                                                padding: '8px 10px',
                                                                /* 增加内边距 */ textAlign: 'center',
                                                                minWidth: '55px'
                                                            },
                                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                                vertical: true,
                                                                align: "center",
                                                                gap: 3,
                                                                children: [
                                                                    " ",
                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.ExclamationCircleOutlined, {
                                                                        style: {
                                                                            color: '#ff4d4f',
                                                                            fontSize: 16
                                                                        }
                                                                    }, void 0, false, {
                                                                        fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                        lineNumber: 994,
                                                                        columnNumber: 35
                                                                    }, void 0),
                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                        strong: true,
                                                                        style: {
                                                                            fontSize: 18,
                                                                            /* 增大数字字体 */ color: '#ff4d4f',
                                                                            lineHeight: 1.2
                                                                        },
                                                                        children: ((_item_stats3 = item.stats) === null || _item_stats3 === void 0 ? void 0 : _item_stats3.overdue) || 0
                                                                    }, void 0, false, {
                                                                        fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                        lineNumber: 997,
                                                                        columnNumber: 35
                                                                    }, void 0),
                                                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                                        style: {
                                                                            fontSize: 11,
                                                                            color: '#666'
                                                                        },
                                                                        children: [
                                                                            " ",
                                                                            "逾期"
                                                                        ]
                                                                    }, void 0, true, {
                                                                        fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                        lineNumber: 1007,
                                                                        columnNumber: 35
                                                                    }, void 0)
                                                                ]
                                                            }, void 0, true, {
                                                                fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                                lineNumber: 993,
                                                                columnNumber: 33
                                                            }, void 0)
                                                        }, void 0, false, {
                                                            fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                            lineNumber: 983,
                                                            columnNumber: 31
                                                        }, void 0)
                                                    }, void 0, false, {
                                                        fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                        lineNumber: 982,
                                                        columnNumber: 29
                                                    }, void 0)
                                                ]
                                            }, void 0, true, {
                                                fileName: "src/pages/personal-center/TeamListCard.tsx",
                                                lineNumber: 877,
                                                columnNumber: 27
                                            }, void 0)
                                        }, void 0, false, {
                                            fileName: "src/pages/personal-center/TeamListCard.tsx",
                                            lineNumber: 876,
                                            columnNumber: 25
                                        }, void 0)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/personal-center/TeamListCard.tsx",
                                    lineNumber: 603,
                                    columnNumber: 23
                                }, void 0)
                            }, void 0, false, {
                                fileName: "src/pages/personal-center/TeamListCard.tsx",
                                lineNumber: 563,
                                columnNumber: 19
                            }, void 0);
                        }
                    }, void 0, false, {
                        fileName: "src/pages/personal-center/TeamListCard.tsx",
                        lineNumber: 558,
                        columnNumber: 15
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/personal-center/TeamListCard.tsx",
                    lineNumber: 548,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "src/pages/personal-center/TeamListCard.tsx",
                lineNumber: 525,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_TeamManagementModal.default, {
                visible: teamManagementModalVisible,
                onCancel: ()=>{
                    setTeamManagementModalVisible(false);
                    setSelectedTeam(null);
                },
                team: selectedTeam,
                onRefresh: fetchTeams
            }, void 0, false, {
                fileName: "src/pages/personal-center/TeamListCard.tsx",
                lineNumber: 1027,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Modal, {
                title: "退出团队",
                open: leaveTeamModalVisible,
                onCancel: ()=>{
                    setLeaveTeamModalVisible(false);
                    setSelectedTeam(null);
                },
                footer: [
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                        onClick: ()=>{
                            setLeaveTeamModalVisible(false);
                            setSelectedTeam(null);
                        },
                        children: "取消"
                    }, "cancel", false, {
                        fileName: "src/pages/personal-center/TeamListCard.tsx",
                        lineNumber: 1046,
                        columnNumber: 11
                    }, void 0),
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                        type: "primary",
                        danger: true,
                        onClick: confirmLeaveTeam,
                        children: "确认退出"
                    }, "leave", false, {
                        fileName: "src/pages/personal-center/TeamListCard.tsx",
                        lineNumber: 1055,
                        columnNumber: 11
                    }, void 0)
                ],
                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                    style: {
                        textAlign: 'center',
                        padding: '20px 0'
                    },
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("p", {
                            children: [
                                "确定要退出团队 ",
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("strong", {
                                    children: selectedTeam === null || selectedTeam === void 0 ? void 0 : selectedTeam.name
                                }, void 0, false, {
                                    fileName: "src/pages/personal-center/TeamListCard.tsx",
                                    lineNumber: 1066,
                                    columnNumber: 22
                                }, this),
                                " 吗？"
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/personal-center/TeamListCard.tsx",
                            lineNumber: 1066,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("p", {
                            style: {
                                color: '#ff4d4f'
                            },
                            children: "退出后您将无法访问该团队的资源和数据"
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/TeamListCard.tsx",
                            lineNumber: 1067,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/personal-center/TeamListCard.tsx",
                    lineNumber: 1065,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "src/pages/personal-center/TeamListCard.tsx",
                lineNumber: 1038,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true);
};
_s(TeamListCard, "y5+VMm+0tRaElR7UimxKu6hz/3M=", false, function() {
    return [
        _max.useModel
    ];
});
_c = TeamListCard;
var _default = TeamListCard;
var _c;
$RefreshReg$(_c, "TeamListCard");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
"src/pages/personal-center/TodoManagement.tsx": function (module, exports, __mako_require__){
"use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _procomponents = __mako_require__("node_modules/@ant-design/pro-components/es/index.js");
var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
var _todo = __mako_require__("src/services/todo.ts");
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
var _s = $RefreshSig$();
const { Text } = _antd.Typography;
const { TabPane } = _antd.Tabs;
const TodoManagement = ()=>{
    _s();
    // TODO数据状态管理
    const [personalTasks, setPersonalTasks] = (0, _react.useState)([]);
    const [todoStats, setTodoStats] = (0, _react.useState)({
        highPriorityCount: 0,
        mediumPriorityCount: 0,
        lowPriorityCount: 0,
        totalCount: 0,
        completedCount: 0,
        completionPercentage: 0
    });
    const [loading, setLoading] = (0, _react.useState)(true);
    const [error, setError] = (0, _react.useState)(null);
    // 待办事项状态管理
    const [todoModalVisible, setTodoModalVisible] = (0, _react.useState)(false);
    const [todoForm] = _antd.Form.useForm();
    const [editingTodoId, setEditingTodoId] = (0, _react.useState)(null);
    // 过滤器状态
    const [activeTab, setActiveTab] = (0, _react.useState)('pending');
    const [searchText, setSearchText] = (0, _react.useState)('');
    // 获取TODO数据
    (0, _react.useEffect)(()=>{
        const fetchTodoData = async ()=>{
            try {
                setLoading(true);
                setError(null);
                console.log('TodoManagement: 开始获取TODO数据');
                // 分别获取TODO列表和统计数据，避免一个失败影响另一个
                const todosPromise = _todo.TodoService.getUserTodos().catch((error)=>{
                    console.error('获取TODO列表失败:', error);
                    return [];
                });
                const statsPromise = _todo.TodoService.getTodoStats().catch((error)=>{
                    console.error('获取TODO统计失败:', error);
                    return {
                        highPriorityCount: 0,
                        mediumPriorityCount: 0,
                        lowPriorityCount: 0,
                        totalCount: 0,
                        completedCount: 0,
                        completionPercentage: 0
                    };
                });
                const [todos, stats] = await Promise.all([
                    todosPromise,
                    statsPromise
                ]);
                console.log('TodoManagement: 获取到TODO列表:', todos);
                console.log('TodoManagement: 获取到统计数据:', stats);
                setPersonalTasks(todos);
                setTodoStats(stats);
            } catch (error) {
                console.error('获取TODO数据时发生未知错误:', error);
                setError('获取TODO数据失败，请刷新页面重试');
            } finally{
                setLoading(false);
            }
        };
        fetchTodoData();
    }, []);
    // 根据激活的标签和搜索文本过滤任务
    const filteredPersonalTasks = (personalTasks || []).filter((task)=>{
        // 根据标签过滤
        if (activeTab === 'pending' && task.status === 1) return false;
        if (activeTab === 'completed' && task.status === 0) return false;
        // 根据搜索文本过滤
        if (searchText && !task.title.toLowerCase().includes(searchText.toLowerCase())) return false;
        return true;
    });
    // 处理待办事项操作
    const handleToggleTodoStatus = async (id)=>{
        try {
            const task = personalTasks.find((t)=>t.id === id);
            if (!task) return;
            const newStatus = task.status === 0 ? 1 : 0;
            await _todo.TodoService.updateTodo(id, {
                status: newStatus
            });
            // 更新本地状态
            setPersonalTasks(personalTasks.map((task)=>task.id === id ? {
                    ...task,
                    status: newStatus
                } : task));
            // 刷新统计数据
            try {
                const stats = await _todo.TodoService.getTodoStats();
                setTodoStats(stats);
            } catch (statsError) {
            // 统计数据刷新失败不影响主要操作
            }
        } catch (error) {
        // 错误处理由响应拦截器统一处理
        }
    };
    const handleAddOrUpdateTodo = async (values)=>{
        try {
            if (editingTodoId) {
                // 更新现有待办事项
                const updatedTodo = await _todo.TodoService.updateTodo(editingTodoId, {
                    title: values.name,
                    priority: values.priority
                });
                setPersonalTasks(personalTasks.map((task)=>task.id === editingTodoId ? updatedTodo : task));
            } else {
                // 添加新待办事项
                const newTodo = await _todo.TodoService.createTodo({
                    title: values.name,
                    priority: values.priority
                });
                setPersonalTasks([
                    newTodo,
                    ...personalTasks
                ]);
            }
            // 刷新统计数据
            try {
                const stats = await _todo.TodoService.getTodoStats();
                setTodoStats(stats);
            } catch (statsError) {
            // 统计数据刷新失败不影响主要操作
            }
            // 重置表单并关闭模态框
            setTodoModalVisible(false);
            setEditingTodoId(null);
            todoForm.resetFields();
        } catch (error) {
        // 错误处理由响应拦截器统一处理
        }
    };
    const handleDeleteTodo = async (id)=>{
        try {
            await _todo.TodoService.deleteTodo(id);
            setPersonalTasks(personalTasks.filter((task)=>task.id !== id));
            // 刷新统计数据
            try {
                const stats = await _todo.TodoService.getTodoStats();
                setTodoStats(stats);
            } catch (statsError) {
            // 统计数据刷新失败不影响主要操作
            }
        } catch (error) {
        // 错误处理由响应拦截器统一处理
        }
    };
    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.ProCard, {
        title: "待办事项/任务列表",
        style: {
            borderRadius: 8,
            height: 'fit-content',
            minHeight: '600px'
        },
        headStyle: {
            borderBottom: '1px solid #f0f0f0',
            paddingBottom: 12
        },
        bodyStyle: {
            padding: '16px'
        },
        children: [
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                style: {
                    marginBottom: 16,
                    padding: '12px 16px',
                    background: '#fafbfc',
                    borderRadius: 8,
                    border: '1px solid #f0f0f0'
                },
                children: [
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                        gutter: [
                            16,
                            16
                        ],
                        style: {
                            marginBottom: 16
                        },
                        children: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                xs: 24,
                                sm: 14,
                                md: 14,
                                lg: 16,
                                xl: 18,
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                    align: "center",
                                    gap: 12,
                                    wrap: "wrap",
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                            style: {
                                                background: '#fff2f0',
                                                border: '1px solid #ffccc7',
                                                borderRadius: 6,
                                                padding: '8px 12px',
                                                display: 'flex',
                                                alignItems: 'center',
                                                gap: 8,
                                                minWidth: 100
                                            },
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                    style: {
                                                        width: 8,
                                                        height: 8,
                                                        borderRadius: '50%',
                                                        background: '#ff4d4f'
                                                    }
                                                }, void 0, false, {
                                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                    lineNumber: 262,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                    style: {
                                                        fontSize: 12,
                                                        color: '#8c8c8c',
                                                        marginRight: 4
                                                    },
                                                    children: "高优先级"
                                                }, void 0, false, {
                                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                    lineNumber: 270,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                    style: {
                                                        fontSize: 14,
                                                        fontWeight: 600,
                                                        color: '#cf1322'
                                                    },
                                                    children: todoStats.highPriorityCount
                                                }, void 0, false, {
                                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                    lineNumber: 273,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                                            lineNumber: 250,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                            style: {
                                                background: '#fffbe6',
                                                border: '1px solid #ffe58f',
                                                borderRadius: 6,
                                                padding: '8px 12px',
                                                display: 'flex',
                                                alignItems: 'center',
                                                gap: 8,
                                                minWidth: 100
                                            },
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                    style: {
                                                        width: 8,
                                                        height: 8,
                                                        borderRadius: '50%',
                                                        background: '#faad14'
                                                    }
                                                }, void 0, false, {
                                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                    lineNumber: 291,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                    style: {
                                                        fontSize: 12,
                                                        color: '#8c8c8c',
                                                        marginRight: 4
                                                    },
                                                    children: "中优先级"
                                                }, void 0, false, {
                                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                    lineNumber: 299,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                    style: {
                                                        fontSize: 14,
                                                        fontWeight: 600,
                                                        color: '#d48806'
                                                    },
                                                    children: todoStats.mediumPriorityCount
                                                }, void 0, false, {
                                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                    lineNumber: 302,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                                            lineNumber: 279,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                            style: {
                                                background: '#fafafa',
                                                border: '1px solid #d9d9d9',
                                                borderRadius: 6,
                                                padding: '8px 12px',
                                                display: 'flex',
                                                alignItems: 'center',
                                                gap: 8,
                                                minWidth: 100
                                            },
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                    style: {
                                                        width: 8,
                                                        height: 8,
                                                        borderRadius: '50%',
                                                        background: '#8c8c8c'
                                                    }
                                                }, void 0, false, {
                                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                    lineNumber: 320,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                    style: {
                                                        fontSize: 12,
                                                        color: '#8c8c8c',
                                                        marginRight: 4
                                                    },
                                                    children: "低优先级"
                                                }, void 0, false, {
                                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                    lineNumber: 328,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                    style: {
                                                        fontSize: 14,
                                                        fontWeight: 600,
                                                        color: '#595959'
                                                    },
                                                    children: todoStats.lowPriorityCount
                                                }, void 0, false, {
                                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                    lineNumber: 331,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                                            lineNumber: 308,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                    lineNumber: 248,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/personal-center/TodoManagement.tsx",
                                lineNumber: 247,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                xs: 24,
                                sm: 10,
                                md: 10,
                                lg: 8,
                                xl: 6,
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                    style: {
                                        background: '#f6ffed',
                                        border: '1px solid #b7eb8f',
                                        borderRadius: 8,
                                        height: '100%',
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'center'
                                    },
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tooltip, {
                                        title: `完成率: ${todoStats.completionPercentage}% (${todoStats.completedCount}/${todoStats.totalCount})`,
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                            align: "center",
                                            gap: 12,
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                    style: {
                                                        fontSize: 12,
                                                        color: '#8c8c8c'
                                                    },
                                                    children: "完成率"
                                                }, void 0, false, {
                                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                    lineNumber: 356,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Progress, {
                                                    percent: todoStats.completionPercentage,
                                                    size: "small",
                                                    style: {
                                                        width: 80
                                                    },
                                                    strokeColor: "#52c41a",
                                                    showInfo: false
                                                }, void 0, false, {
                                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                    lineNumber: 359,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                    style: {
                                                        fontSize: 16,
                                                        fontWeight: 600,
                                                        color: '#389e0d'
                                                    },
                                                    children: [
                                                        todoStats.completionPercentage,
                                                        "%"
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                    lineNumber: 366,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                                            lineNumber: 355,
                                            columnNumber: 17
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "src/pages/personal-center/TodoManagement.tsx",
                                        lineNumber: 352,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                    lineNumber: 340,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/personal-center/TodoManagement.tsx",
                                lineNumber: 339,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/personal-center/TodoManagement.tsx",
                        lineNumber: 245,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                        gutter: [
                            16,
                            0
                        ],
                        children: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                xs: 24,
                                sm: 24,
                                md: 16,
                                lg: 18,
                                xl: 20,
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input.Search, {
                                    placeholder: "搜索任务...",
                                    allowClear: true,
                                    prefix: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.SearchOutlined, {}, void 0, false, {
                                        fileName: "src/pages/personal-center/TodoManagement.tsx",
                                        lineNumber: 387,
                                        columnNumber: 23
                                    }, void 0),
                                    value: searchText,
                                    onChange: (e)=>setSearchText(e.target.value),
                                    style: {
                                        width: '100%'
                                    },
                                    size: "middle"
                                }, void 0, false, {
                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                    lineNumber: 384,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/personal-center/TodoManagement.tsx",
                                lineNumber: 383,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                xs: 24,
                                sm: 24,
                                md: 8,
                                lg: 6,
                                xl: 4,
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                    type: "primary",
                                    icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.PlusOutlined, {}, void 0, false, {
                                        fileName: "src/pages/personal-center/TodoManagement.tsx",
                                        lineNumber: 398,
                                        columnNumber: 21
                                    }, void 0),
                                    onClick: ()=>{
                                        setEditingTodoId(null);
                                        todoForm.resetFields();
                                        setTodoModalVisible(true);
                                    },
                                    style: {
                                        background: '#1890ff',
                                        borderColor: '#1890ff',
                                        boxShadow: '0 2px 4px rgba(24, 144, 255, 0.3)',
                                        fontWeight: 500,
                                        width: '100%'
                                    },
                                    size: "middle",
                                    children: "添加新任务"
                                }, void 0, false, {
                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                    lineNumber: 396,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/personal-center/TodoManagement.tsx",
                                lineNumber: 395,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/personal-center/TodoManagement.tsx",
                        lineNumber: 382,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "src/pages/personal-center/TodoManagement.tsx",
                lineNumber: 235,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tabs, {
                activeKey: activeTab,
                onChange: (key)=>setActiveTab(key),
                size: "middle",
                style: {
                    marginBottom: 8
                },
                children: [
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(TabPane, {
                        tab: "全部"
                    }, "all", false, {
                        fileName: "src/pages/personal-center/TodoManagement.tsx",
                        lineNumber: 426,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(TabPane, {
                        tab: "待处理"
                    }, "pending", false, {
                        fileName: "src/pages/personal-center/TodoManagement.tsx",
                        lineNumber: 427,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(TabPane, {
                        tab: "已完成"
                    }, "completed", false, {
                        fileName: "src/pages/personal-center/TodoManagement.tsx",
                        lineNumber: 428,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "src/pages/personal-center/TodoManagement.tsx",
                lineNumber: 420,
                columnNumber: 7
            }, this),
            error ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Alert, {
                message: "TODO数据加载失败",
                description: error,
                type: "error",
                showIcon: true,
                style: {
                    marginBottom: 16
                }
            }, void 0, false, {
                fileName: "src/pages/personal-center/TodoManagement.tsx",
                lineNumber: 433,
                columnNumber: 9
            }, this) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Spin, {
                spinning: loading,
                children: [
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.ProList, {
                        dataSource: filteredPersonalTasks,
                        renderItem: (item)=>{
                            return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                className: "todo-item",
                                style: {
                                    padding: '10px 16px',
                                    marginBottom: 12,
                                    borderRadius: 8,
                                    background: '#fff',
                                    opacity: item.status === 1 ? 0.7 : 1,
                                    borderLeft: `3px solid ${item.status === 1 ? '#52c41a' : item.priority === 3 ? '#ff4d4f' : item.priority === 2 ? '#faad14' : '#8c8c8c'}`,
                                    boxShadow: '0 1px 4px rgba(0,0,0,0.05)'
                                },
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                    align: "center",
                                    gap: 12,
                                    style: {
                                        width: '100%'
                                    },
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                            vertical: true,
                                            align: "center",
                                            children: [
                                                item.status === 1 ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                                    align: "center",
                                                    justify: "center",
                                                    style: {
                                                        width: 22,
                                                        height: 22,
                                                        borderRadius: '50%',
                                                        background: '#52c41a'
                                                    },
                                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CheckOutlined, {
                                                        style: {
                                                            color: '#fff',
                                                            fontSize: 12
                                                        }
                                                    }, void 0, false, {
                                                        fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                        lineNumber: 480,
                                                        columnNumber: 27
                                                    }, void 0)
                                                }, void 0, false, {
                                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                    lineNumber: 470,
                                                    columnNumber: 25
                                                }, void 0) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                    style: {
                                                        width: 18,
                                                        height: 18,
                                                        borderRadius: '50%',
                                                        border: `2px solid ${item.priority === 3 ? '#ff4d4f' : item.priority === 2 ? '#faad14' : '#8c8c8c'}`
                                                    }
                                                }, void 0, false, {
                                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                    lineNumber: 485,
                                                    columnNumber: 25
                                                }, void 0),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                                    style: {
                                                        width: 2,
                                                        height: 24,
                                                        background: '#f0f0f0',
                                                        marginTop: 4
                                                    }
                                                }, void 0, false, {
                                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                    lineNumber: 501,
                                                    columnNumber: 23
                                                }, void 0)
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                                            lineNumber: 468,
                                            columnNumber: 21
                                        }, void 0),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Flex, {
                                            vertical: true,
                                            style: {
                                                flex: 1
                                            },
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                    style: {
                                                        fontSize: 14,
                                                        fontWeight: item.priority === 3 ? 500 : 'normal',
                                                        textDecoration: item.status === 1 ? 'line-through' : 'none',
                                                        color: item.status === 1 ? '#8c8c8c' : '#262626'
                                                    },
                                                    children: item.title
                                                }, void 0, false, {
                                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                    lineNumber: 513,
                                                    columnNumber: 23
                                                }, void 0),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                    align: "center",
                                                    size: 6,
                                                    style: {
                                                        marginTop: 4
                                                    },
                                                    children: [
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CalendarOutlined, {
                                                            style: {
                                                                fontSize: 12,
                                                                color: '#8c8c8c'
                                                            }
                                                        }, void 0, false, {
                                                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                            lineNumber: 527,
                                                            columnNumber: 25
                                                        }, void 0),
                                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                                            type: "secondary",
                                                            style: {
                                                                fontSize: 12
                                                            },
                                                            children: [
                                                                "创建于:",
                                                                ' ',
                                                                new Date(item.createdAt).toLocaleDateString('zh-CN')
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                            lineNumber: 533,
                                                            columnNumber: 25
                                                        }, void 0)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                    lineNumber: 526,
                                                    columnNumber: 23
                                                }, void 0)
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                                            lineNumber: 512,
                                            columnNumber: 21
                                        }, void 0),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Dropdown, {
                                            trigger: [
                                                'click'
                                            ],
                                            menu: {
                                                items: [
                                                    {
                                                        key: 'complete',
                                                        label: item.status === 1 ? '标记未完成' : '标记完成',
                                                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CheckOutlined, {
                                                            style: {
                                                                color: item.status === 1 ? '#8c8c8c' : '#52c41a',
                                                                fontSize: 14
                                                            }
                                                        }, void 0, false, {
                                                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                            lineNumber: 550,
                                                            columnNumber: 31
                                                        }, void 0)
                                                    },
                                                    {
                                                        key: 'edit',
                                                        label: '编辑任务',
                                                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.EditOutlined, {
                                                            style: {
                                                                color: '#8c8c8c'
                                                            }
                                                        }, void 0, false, {
                                                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                            lineNumber: 562,
                                                            columnNumber: 35
                                                        }, void 0)
                                                    },
                                                    {
                                                        key: 'delete',
                                                        label: '删除任务',
                                                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.DeleteOutlined, {
                                                            style: {
                                                                color: '#ff4d4f'
                                                            }
                                                        }, void 0, false, {
                                                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                            lineNumber: 568,
                                                            columnNumber: 31
                                                        }, void 0),
                                                        danger: true
                                                    }
                                                ],
                                                onClick: ({ key })=>{
                                                    if (key === 'complete') handleToggleTodoStatus(item.id);
                                                    else if (key === 'edit') {
                                                        setEditingTodoId(item.id);
                                                        todoForm.setFieldsValue({
                                                            name: item.title,
                                                            priority: item.priority
                                                        });
                                                        setTodoModalVisible(true);
                                                    } else if (key === 'delete') handleDeleteTodo(item.id);
                                                }
                                            },
                                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                                type: "text",
                                                size: "small",
                                                icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.MoreOutlined, {}, void 0, false, {
                                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                    lineNumber: 592,
                                                    columnNumber: 31
                                                }, void 0),
                                                style: {
                                                    width: 32,
                                                    height: 32
                                                }
                                            }, void 0, false, {
                                                fileName: "src/pages/personal-center/TodoManagement.tsx",
                                                lineNumber: 589,
                                                columnNumber: 23
                                            }, void 0)
                                        }, void 0, false, {
                                            fileName: "src/pages/personal-center/TodoManagement.tsx",
                                            lineNumber: 541,
                                            columnNumber: 21
                                        }, void 0)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                    lineNumber: 466,
                                    columnNumber: 19
                                }, void 0)
                            }, void 0, false, {
                                fileName: "src/pages/personal-center/TodoManagement.tsx",
                                lineNumber: 446,
                                columnNumber: 17
                            }, void 0);
                        }
                    }, void 0, false, {
                        fileName: "src/pages/personal-center/TodoManagement.tsx",
                        lineNumber: 442,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.ModalForm, {
                        title: editingTodoId ? '编辑待办事项' : '新增待办事项',
                        open: todoModalVisible,
                        onOpenChange: (visible)=>{
                            setTodoModalVisible(visible);
                            if (!visible) {
                                setEditingTodoId(null);
                                todoForm.resetFields();
                            }
                        },
                        form: todoForm,
                        layout: "vertical",
                        onFinish: handleAddOrUpdateTodo,
                        autoComplete: "off",
                        width: 500,
                        modalProps: {
                            centered: true,
                            destroyOnClose: true,
                            maskClosable: true,
                            keyboard: true,
                            forceRender: false
                        },
                        submitter: {
                            searchConfig: {
                                submitText: editingTodoId ? '更新任务' : '创建任务',
                                resetText: '取消'
                            },
                            submitButtonProps: {
                                style: {
                                    background: '#1890ff',
                                    borderColor: '#1890ff',
                                    boxShadow: '0 2px 4px rgba(24, 144, 255, 0.3)'
                                },
                                icon: editingTodoId ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.EditOutlined, {}, void 0, false, {
                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                    lineNumber: 636,
                                    columnNumber: 39
                                }, void 0) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.PlusOutlined, {}, void 0, false, {
                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                    lineNumber: 636,
                                    columnNumber: 58
                                }, void 0)
                            },
                            resetButtonProps: {
                                style: {
                                    borderColor: '#d9d9d9'
                                }
                            },
                            onReset: ()=>{
                                setTodoModalVisible(false);
                                setEditingTodoId(null);
                                todoForm.resetFields();
                            }
                        },
                        preserve: false,
                        children: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                name: "name",
                                label: "任务名称",
                                rules: [
                                    {
                                        required: true,
                                        message: '请输入任务名称'
                                    }
                                ],
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                                    placeholder: "请输入任务名称",
                                    size: "large",
                                    style: {
                                        borderRadius: 6
                                    }
                                }, void 0, false, {
                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                    lineNumber: 656,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/personal-center/TodoManagement.tsx",
                                lineNumber: 651,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                name: "priority",
                                label: "优先级",
                                initialValue: 2,
                                rules: [
                                    {
                                        required: true,
                                        message: '请选择优先级'
                                    }
                                ],
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Select, {
                                    size: "large",
                                    options: [
                                        {
                                            value: 3,
                                            label: '高优先级'
                                        },
                                        {
                                            value: 2,
                                            label: '中优先级'
                                        },
                                        {
                                            value: 1,
                                            label: '低优先级'
                                        }
                                    ],
                                    style: {
                                        borderRadius: 6
                                    }
                                }, void 0, false, {
                                    fileName: "src/pages/personal-center/TodoManagement.tsx",
                                    lineNumber: 669,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/personal-center/TodoManagement.tsx",
                                lineNumber: 663,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/personal-center/TodoManagement.tsx",
                        lineNumber: 603,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "src/pages/personal-center/TodoManagement.tsx",
                lineNumber: 441,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "src/pages/personal-center/TodoManagement.tsx",
        lineNumber: 219,
        columnNumber: 5
    }, this);
};
_s(TodoManagement, "BRDE78r/O6qdRZ096tTY2qeEVcA=", false, function() {
    return [
        _antd.Form.useForm
    ];
});
_c = TodoManagement;
var _default = TodoManagement;
var _c;
$RefreshReg$(_c, "TodoManagement");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
"src/pages/personal-center/UnifiedSettingsModal.tsx": function (module, exports, __mako_require__){
"use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
var _s = $RefreshSig$();
const { Title, Text } = _antd.Typography;
/**
 * 统一设置Modal组件
 *
 * 提供个人信息修改和新建团队功能的统一界面。
 * 使用Tab页面结构组织不同的设置功能。
 *
 * 主要功能：
 * 1. 个人信息修改Tab：编辑用户基本信息
 * 2. 新建团队Tab：创建新团队
 *
 * Props:
 * - visible: 控制Modal显示/隐藏
 * - onCancel: 取消操作回调
 * - onSuccess: 操作成功回调
 * - userInfo: 当前用户信息
 */ const UnifiedSettingsModal = ({ visible, onCancel, onSuccess, userInfo })=>{
    _s();
    const [personalForm] = _antd.Form.useForm();
    const [teamForm] = _antd.Form.useForm();
    const [activeTab, setActiveTab] = (0, _react.useState)('personal');
    // 当Modal打开时，填充个人信息表单数据
    (0, _react.useEffect)(()=>{
        if (visible && userInfo) personalForm.setFieldsValue({
            name: userInfo.name,
            email: userInfo.email,
            telephone: userInfo.telephone,
            position: userInfo.position
        });
    }, [
        visible,
        userInfo,
        personalForm
    ]);
    // 处理个人信息提交
    const handlePersonalSubmit = async ()=>{
        try {
            const values = await personalForm.validateFields();
            console.log('更新个人信息:', values);
            // TODO: 调用更新用户信息的API
            // await UserService.updateUserProfile(values);
            _antd.message.success('个人信息更新成功！');
            onSuccess === null || onSuccess === void 0 || onSuccess();
            onCancel();
        } catch (error) {
            console.error('更新个人信息失败:', error);
            _antd.message.error('更新个人信息失败，请稍后重试');
        }
    };
    // 处理团队创建提交
    const handleTeamSubmit = async ()=>{
        try {
            const values = await teamForm.validateFields();
            console.log('创建团队:', values);
            // TODO: 调用创建团队的API
            // await TeamService.createTeam(values);
            _antd.message.success('团队创建成功！');
            teamForm.resetFields();
            onSuccess === null || onSuccess === void 0 || onSuccess();
            onCancel();
        } catch (error) {
            console.error('创建团队失败:', error);
            _antd.message.error('创建团队失败，请稍后重试');
        }
    };
    // 处理取消操作
    const handleCancel = ()=>{
        personalForm.resetFields();
        teamForm.resetFields();
        setActiveTab('personal');
        onCancel();
    };
    // 根据当前Tab决定提交操作
    const handleOk = ()=>{
        if (activeTab === 'personal') handlePersonalSubmit();
        else handleTeamSubmit();
    };
    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Modal, {
        title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
            align: "center",
            children: [
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.SettingOutlined, {
                    style: {
                        fontSize: 18,
                        color: '#1890ff'
                    }
                }, void 0, false, {
                    fileName: "src/pages/personal-center/UnifiedSettingsModal.tsx",
                    lineNumber: 120,
                    columnNumber: 11
                }, void 0),
                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                    level: 4,
                    style: {
                        margin: 0,
                        fontSize: 16
                    },
                    children: "设置"
                }, void 0, false, {
                    fileName: "src/pages/personal-center/UnifiedSettingsModal.tsx",
                    lineNumber: 121,
                    columnNumber: 11
                }, void 0)
            ]
        }, void 0, true, {
            fileName: "src/pages/personal-center/UnifiedSettingsModal.tsx",
            lineNumber: 119,
            columnNumber: 9
        }, void 0),
        open: visible,
        onOk: handleOk,
        onCancel: handleCancel,
        okText: activeTab === 'personal' ? '保存设置' : '创建团队',
        cancelText: "取消",
        destroyOnClose: true,
        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tabs, {
            activeKey: activeTab,
            onChange: setActiveTab,
            style: {
                marginTop: -8
            },
            items: [
                {
                    key: 'personal',
                    label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                        align: "center",
                        children: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {}, void 0, false, {
                                fileName: "src/pages/personal-center/UnifiedSettingsModal.tsx",
                                lineNumber: 144,
                                columnNumber: 17
                            }, void 0),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("span", {
                                children: "个人信息"
                            }, void 0, false, {
                                fileName: "src/pages/personal-center/UnifiedSettingsModal.tsx",
                                lineNumber: 145,
                                columnNumber: 17
                            }, void 0)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/personal-center/UnifiedSettingsModal.tsx",
                        lineNumber: 143,
                        columnNumber: 15
                    }, void 0),
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                        children: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                style: {
                                    marginBottom: 16
                                },
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                    style: {
                                        color: '#8c8c8c',
                                        fontSize: 14
                                    },
                                    children: "编辑您的个人信息和偏好设置"
                                }, void 0, false, {
                                    fileName: "src/pages/personal-center/UnifiedSettingsModal.tsx",
                                    lineNumber: 151,
                                    columnNumber: 19
                                }, void 0)
                            }, void 0, false, {
                                fileName: "src/pages/personal-center/UnifiedSettingsModal.tsx",
                                lineNumber: 150,
                                columnNumber: 17
                            }, void 0),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form, {
                                form: personalForm,
                                layout: "vertical",
                                requiredMark: false,
                                autoComplete: "off",
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                        label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                            style: {
                                                fontWeight: 600,
                                                fontSize: 14
                                            },
                                            children: "姓名"
                                        }, void 0, false, {
                                            fileName: "src/pages/personal-center/UnifiedSettingsModal.tsx",
                                            lineNumber: 165,
                                            columnNumber: 23
                                        }, void 0),
                                        name: "name",
                                        rules: [
                                            {
                                                required: true,
                                                message: '请输入姓名'
                                            },
                                            {
                                                min: 2,
                                                message: '姓名至少2个字符'
                                            },
                                            {
                                                max: 20,
                                                message: '姓名不能超过20个字符'
                                            }
                                        ],
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                                            placeholder: "请输入姓名",
                                            style: {
                                                borderRadius: 6,
                                                fontSize: 14
                                            }
                                        }, void 0, false, {
                                            fileName: "src/pages/personal-center/UnifiedSettingsModal.tsx",
                                            lineNumber: 176,
                                            columnNumber: 21
                                        }, void 0)
                                    }, void 0, false, {
                                        fileName: "src/pages/personal-center/UnifiedSettingsModal.tsx",
                                        lineNumber: 163,
                                        columnNumber: 19
                                    }, void 0),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                        label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                            style: {
                                                fontWeight: 600,
                                                fontSize: 14
                                            },
                                            children: "职位"
                                        }, void 0, false, {
                                            fileName: "src/pages/personal-center/UnifiedSettingsModal.tsx",
                                            lineNumber: 188,
                                            columnNumber: 23
                                        }, void 0),
                                        name: "position",
                                        rules: [
                                            {
                                                max: 50,
                                                message: '职位不能超过50个字符'
                                            }
                                        ],
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                                            placeholder: "请输入职位（可选）",
                                            style: {
                                                borderRadius: 6,
                                                fontSize: 14
                                            }
                                        }, void 0, false, {
                                            fileName: "src/pages/personal-center/UnifiedSettingsModal.tsx",
                                            lineNumber: 197,
                                            columnNumber: 21
                                        }, void 0)
                                    }, void 0, false, {
                                        fileName: "src/pages/personal-center/UnifiedSettingsModal.tsx",
                                        lineNumber: 186,
                                        columnNumber: 19
                                    }, void 0),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                        label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                            style: {
                                                fontWeight: 600,
                                                fontSize: 14
                                            },
                                            children: "邮箱"
                                        }, void 0, false, {
                                            fileName: "src/pages/personal-center/UnifiedSettingsModal.tsx",
                                            lineNumber: 209,
                                            columnNumber: 23
                                        }, void 0),
                                        name: "email",
                                        rules: [
                                            {
                                                required: true,
                                                message: '请输入邮箱'
                                            },
                                            {
                                                type: 'email',
                                                message: '请输入有效的邮箱地址'
                                            }
                                        ],
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                                            placeholder: "请输入邮箱",
                                            style: {
                                                borderRadius: 6,
                                                fontSize: 14
                                            }
                                        }, void 0, false, {
                                            fileName: "src/pages/personal-center/UnifiedSettingsModal.tsx",
                                            lineNumber: 219,
                                            columnNumber: 21
                                        }, void 0)
                                    }, void 0, false, {
                                        fileName: "src/pages/personal-center/UnifiedSettingsModal.tsx",
                                        lineNumber: 207,
                                        columnNumber: 19
                                    }, void 0),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                        label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                            style: {
                                                fontWeight: 600,
                                                fontSize: 14
                                            },
                                            children: "电话"
                                        }, void 0, false, {
                                            fileName: "src/pages/personal-center/UnifiedSettingsModal.tsx",
                                            lineNumber: 231,
                                            columnNumber: 23
                                        }, void 0),
                                        name: "telephone",
                                        rules: [
                                            {
                                                pattern: /^1[3-9]\d{9}$/,
                                                message: '请输入有效的手机号码'
                                            }
                                        ],
                                        children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                                            placeholder: "请输入电话（可选）",
                                            style: {
                                                borderRadius: 6,
                                                fontSize: 14
                                            }
                                        }, void 0, false, {
                                            fileName: "src/pages/personal-center/UnifiedSettingsModal.tsx",
                                            lineNumber: 240,
                                            columnNumber: 21
                                        }, void 0)
                                    }, void 0, false, {
                                        fileName: "src/pages/personal-center/UnifiedSettingsModal.tsx",
                                        lineNumber: 229,
                                        columnNumber: 19
                                    }, void 0)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/personal-center/UnifiedSettingsModal.tsx",
                                lineNumber: 156,
                                columnNumber: 17
                            }, void 0)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/personal-center/UnifiedSettingsModal.tsx",
                        lineNumber: 149,
                        columnNumber: 15
                    }, void 0)
                },
                {
                    key: 'team',
                    label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                        align: "center",
                        children: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.TeamOutlined, {}, void 0, false, {
                                fileName: "src/pages/personal-center/UnifiedSettingsModal.tsx",
                                lineNumber: 256,
                                columnNumber: 17
                            }, void 0),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("span", {
                                children: "新建团队"
                            }, void 0, false, {
                                fileName: "src/pages/personal-center/UnifiedSettingsModal.tsx",
                                lineNumber: 257,
                                columnNumber: 17
                            }, void 0)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/personal-center/UnifiedSettingsModal.tsx",
                        lineNumber: 255,
                        columnNumber: 15
                    }, void 0),
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                        children: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                style: {
                                    marginBottom: 24,
                                    textAlign: 'center'
                                },
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                    style: {
                                        color: '#8c8c8c',
                                        fontSize: 14,
                                        lineHeight: 1.6
                                    },
                                    children: "创建一个新的团队来协作管理项目和任务"
                                }, void 0, false, {
                                    fileName: "src/pages/personal-center/UnifiedSettingsModal.tsx",
                                    lineNumber: 263,
                                    columnNumber: 19
                                }, void 0)
                            }, void 0, false, {
                                fileName: "src/pages/personal-center/UnifiedSettingsModal.tsx",
                                lineNumber: 262,
                                columnNumber: 17
                            }, void 0),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form, {
                                form: teamForm,
                                layout: "vertical",
                                requiredMark: false,
                                autoComplete: "off",
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                                    label: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                        style: {
                                            fontWeight: 600,
                                            fontSize: 15
                                        },
                                        children: "团队名称"
                                    }, void 0, false, {
                                        fileName: "src/pages/personal-center/UnifiedSettingsModal.tsx",
                                        lineNumber: 277,
                                        columnNumber: 23
                                    }, void 0),
                                    name: "teamName",
                                    rules: [
                                        {
                                            required: true,
                                            message: '请输入团队名称'
                                        },
                                        {
                                            min: 2,
                                            message: '团队名称至少2个字符'
                                        },
                                        {
                                            max: 50,
                                            message: '团队名称不能超过50个字符'
                                        }
                                    ],
                                    style: {
                                        marginBottom: 0
                                    },
                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                                        placeholder: "请输入团队名称",
                                        size: "large",
                                        style: {
                                            borderRadius: 8,
                                            fontSize: 15,
                                            padding: '12px 16px',
                                            border: '2px solid #d9d9d9',
                                            transition: 'all 0.3s ease'
                                        },
                                        onFocus: (e)=>{
                                            e.target.style.borderColor = '#1890ff';
                                            e.target.style.boxShadow = '0 0 0 2px rgba(24, 144, 255, 0.1)';
                                        },
                                        onBlur: (e)=>{
                                            e.target.style.borderColor = '#d9d9d9';
                                            e.target.style.boxShadow = 'none';
                                        }
                                    }, void 0, false, {
                                        fileName: "src/pages/personal-center/UnifiedSettingsModal.tsx",
                                        lineNumber: 289,
                                        columnNumber: 21
                                    }, void 0)
                                }, void 0, false, {
                                    fileName: "src/pages/personal-center/UnifiedSettingsModal.tsx",
                                    lineNumber: 275,
                                    columnNumber: 19
                                }, void 0)
                            }, void 0, false, {
                                fileName: "src/pages/personal-center/UnifiedSettingsModal.tsx",
                                lineNumber: 268,
                                columnNumber: 17
                            }, void 0)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/personal-center/UnifiedSettingsModal.tsx",
                        lineNumber: 261,
                        columnNumber: 15
                    }, void 0)
                }
            ]
        }, void 0, false, {
            fileName: "src/pages/personal-center/UnifiedSettingsModal.tsx",
            lineNumber: 135,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "src/pages/personal-center/UnifiedSettingsModal.tsx",
        lineNumber: 117,
        columnNumber: 5
    }, this);
};
_s(UnifiedSettingsModal, "DDUCm/Qsce57NmLc3imUABC/62A=", false, function() {
    return [
        _antd.Form.useForm,
        _antd.Form.useForm
    ];
});
_c = UnifiedSettingsModal;
var _default = UnifiedSettingsModal;
var _c;
$RefreshReg$(_c, "UnifiedSettingsModal");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
"src/pages/personal-center/components/TeamManagementModal.tsx": function (module, exports, __mako_require__){
/**
 * 团队管理模态框组件
 *
 * 功能特性：
 * - 团队成员列表和管理
 * - 团队邀请功能
 * - 团队信息编辑
 * - 团队删除功能
 * - 权限控制，只有团队创建者可以访问管理功能
 */ "use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_default = __mako_require__("@swc/helpers/_/_interop_require_default");
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _react = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/react/index.js"));
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _procomponents = __mako_require__("node_modules/@ant-design/pro-components/es/index.js");
var _icons = __mako_require__("node_modules/@ant-design/icons/es/index.js");
var _dayjs = /*#__PURE__*/ _interop_require_default._(__mako_require__("node_modules/dayjs/dayjs.min.js"));
var _team = __mako_require__("src/services/team.ts");
var _invitation = __mako_require__("src/services/invitation.ts");
var _InvitationStatus = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/components/InvitationStatus.tsx"));
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
var _s = $RefreshSig$();
const { Title, Text, Paragraph } = _antd.Typography;
const { TextArea } = _antd.Input;
const { TabPane } = _antd.Tabs;
const TeamManagementModal = ({ visible, onCancel, team, onRefresh })=>{
    _s();
    const [loading, setLoading] = (0, _react.useState)(false);
    const [members, setMembers] = (0, _react.useState)([]);
    const [invitations, setInvitations] = (0, _react.useState)([]);
    const [searchText, setSearchText] = (0, _react.useState)('');
    const [selectedRowKeys, setSelectedRowKeys] = (0, _react.useState)([]);
    // 团队编辑相关状态
    const [editModalVisible, setEditModalVisible] = (0, _react.useState)(false);
    const [updating, setUpdating] = (0, _react.useState)(false);
    const [form] = _antd.Form.useForm();
    // 团队删除相关状态
    const [deleteModalVisible, setDeleteModalVisible] = (0, _react.useState)(false);
    const [deleteConfirmText, setDeleteConfirmText] = (0, _react.useState)('');
    const [deleting, setDeleting] = (0, _react.useState)(false);
    // 邀请成员相关状态
    const [inviteModalVisible, setInviteModalVisible] = (0, _react.useState)(false);
    const [inviteForm] = _antd.Form.useForm();
    (0, _react.useEffect)(()=>{
        if (visible && team) {
            fetchMembers();
            fetchInvitations();
        }
    }, [
        visible,
        team
    ]);
    // 获取团队成员列表
    const fetchMembers = async ()=>{
        if (!team) return;
        try {
            setLoading(true);
            const response = await _team.TeamService.getTeamMembers();
            setMembers(response.list || []);
        } catch (error) {
            console.error('获取团队成员失败:', error);
            _antd.message.error('获取团队成员失败');
        } finally{
            setLoading(false);
        }
    };
    // 获取邀请列表
    const fetchInvitations = async ()=>{
        if (!team) return;
        try {
            const response = await _invitation.InvitationService.getCurrentTeamInvitations();
            setInvitations(response || []);
        } catch (error) {
            console.error('获取邀请列表失败:', error);
        }
    };
    // 移除团队成员
    const handleRemoveMember = async (memberId, memberName)=>{
        try {
            await _team.TeamService.removeMember(memberId);
            _antd.message.success(`已移除成员 ${memberName}`);
            fetchMembers();
            onRefresh();
        } catch (error) {
            console.error('移除成员失败:', error);
            _antd.message.error('移除成员失败');
        }
    };
    // 邀请成员
    const handleInviteMembers = async (values)=>{
        try {
            const emailList = values.emails.split(',').map((email)=>email.trim()).filter((email)=>email);
            const request = {
                emails: emailList,
                message: values.message
            };
            await _team.TeamService.inviteMembers(request);
            _antd.message.success('邀请已发送');
            setInviteModalVisible(false);
            inviteForm.resetFields();
            fetchInvitations();
        } catch (error) {
            console.error('邀请成员失败:', error);
            _antd.message.error('邀请成员失败');
        }
    };
    // 更新团队信息
    const handleUpdateTeam = async (values)=>{
        if (!team) return;
        try {
            setUpdating(true);
            await _team.TeamService.updateCurrentTeam(values);
            _antd.message.success('团队信息更新成功');
            setEditModalVisible(false);
            form.resetFields();
            onRefresh();
        } catch (error) {
            console.error('更新团队信息失败:', error);
            _antd.message.error('更新团队信息失败');
        } finally{
            setUpdating(false);
        }
    };
    // 删除团队
    const handleDeleteTeam = async ()=>{
        if (!team || deleteConfirmText !== team.name) {
            _antd.message.error('请输入正确的团队名称');
            return;
        }
        try {
            setDeleting(true);
            await _team.TeamService.deleteCurrentTeam();
            _antd.message.success('团队已删除');
            setDeleteModalVisible(false);
            onCancel();
            onRefresh();
        } catch (error) {
            console.error('删除团队失败:', error);
            _antd.message.error('删除团队失败');
        } finally{
            setDeleting(false);
        }
    };
    // 权限检查
    const hasManagePermission = (team === null || team === void 0 ? void 0 : team.isCreator) || false;
    if (!team) return null;
    // 成员表格列定义
    const memberColumns = [
        {
            title: '成员信息',
            dataIndex: 'name',
            key: 'name',
            render: (_, record)=>{
                var _record_name_charAt, _record_name;
                return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Avatar, {
                            size: "small",
                            icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {}, void 0, false, {
                                fileName: "src/pages/personal-center/components/TeamManagementModal.tsx",
                                lineNumber: 226,
                                columnNumber: 38
                            }, void 0),
                            children: (_record_name = record.name) === null || _record_name === void 0 ? void 0 : (_record_name_charAt = _record_name.charAt(0)) === null || _record_name_charAt === void 0 ? void 0 : _record_name_charAt.toUpperCase()
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/components/TeamManagementModal.tsx",
                            lineNumber: 226,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                            children: [
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                    style: {
                                        fontWeight: 500
                                    },
                                    children: record.name
                                }, void 0, false, {
                                    fileName: "src/pages/personal-center/components/TeamManagementModal.tsx",
                                    lineNumber: 230,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                    style: {
                                        fontSize: 12,
                                        color: '#666'
                                    },
                                    children: record.email
                                }, void 0, false, {
                                    fileName: "src/pages/personal-center/components/TeamManagementModal.tsx",
                                    lineNumber: 231,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/personal-center/components/TeamManagementModal.tsx",
                            lineNumber: 229,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/personal-center/components/TeamManagementModal.tsx",
                    lineNumber: 225,
                    columnNumber: 9
                }, this);
            }
        },
        {
            title: '角色',
            dataIndex: 'isCreator',
            key: 'role',
            render: (_, record)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
                    icon: record.isCreator ? /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.CrownOutlined, {}, void 0, false, {
                        fileName: "src/pages/personal-center/components/TeamManagementModal.tsx",
                        lineNumber: 242,
                        columnNumber: 36
                    }, void 0) : /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.UserOutlined, {}, void 0, false, {
                        fileName: "src/pages/personal-center/components/TeamManagementModal.tsx",
                        lineNumber: 242,
                        columnNumber: 56
                    }, void 0),
                    color: record.isCreator ? 'gold' : 'blue',
                    children: record.isCreator ? '管理员' : '成员'
                }, void 0, false, {
                    fileName: "src/pages/personal-center/components/TeamManagementModal.tsx",
                    lineNumber: 241,
                    columnNumber: 9
                }, this)
        },
        {
            title: '加入时间',
            dataIndex: 'assignedAt',
            key: 'assignedAt',
            render: (text)=>(0, _dayjs.default)(text).format('YYYY-MM-DD HH:mm')
        },
        {
            title: '最后访问',
            dataIndex: 'lastAccessTime',
            key: 'lastAccessTime',
            render: (text)=>text ? (0, _dayjs.default)(text).format('YYYY-MM-DD HH:mm') : '-'
        },
        {
            title: '状态',
            dataIndex: 'isActive',
            key: 'isActive',
            render: (isActive)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tag, {
                    color: isActive ? 'success' : 'error',
                    children: isActive ? '启用' : '停用'
                }, void 0, false, {
                    fileName: "src/pages/personal-center/components/TeamManagementModal.tsx",
                    lineNumber: 266,
                    columnNumber: 9
                }, this)
        }
    ];
    if (hasManagePermission) memberColumns.push({
        title: '操作',
        key: 'action',
        render: (_, record)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                children: !record.isCreator && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Popconfirm, {
                    title: "确定要移除这个成员吗？",
                    onConfirm: ()=>handleRemoveMember(record.id, record.name),
                    okText: "确定",
                    cancelText: "取消",
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                        type: "text",
                        danger: true,
                        size: "small",
                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.DeleteOutlined, {}, void 0, false, {
                            fileName: "src/pages/personal-center/components/TeamManagementModal.tsx",
                            lineNumber: 290,
                            columnNumber: 23
                        }, void 0),
                        children: "移除"
                    }, void 0, false, {
                        fileName: "src/pages/personal-center/components/TeamManagementModal.tsx",
                        lineNumber: 286,
                        columnNumber: 15
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/personal-center/components/TeamManagementModal.tsx",
                    lineNumber: 280,
                    columnNumber: 13
                }, this)
            }, void 0, false, {
                fileName: "src/pages/personal-center/components/TeamManagementModal.tsx",
                lineNumber: 278,
                columnNumber: 9
            }, this)
    });
    // 邀请记录表格列定义
    const invitationColumns = [
        {
            title: '邀请信息',
            dataIndex: 'inviteeEmail',
            key: 'inviteeEmail',
            render: (_, record)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                            style: {
                                fontWeight: 500
                            },
                            children: record.inviteeEmail
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/components/TeamManagementModal.tsx",
                            lineNumber: 309,
                            columnNumber: 11
                        }, this),
                        record.inviteeName && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                            style: {
                                fontSize: 12,
                                color: '#666'
                            },
                            children: record.inviteeName
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/components/TeamManagementModal.tsx",
                            lineNumber: 311,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/personal-center/components/TeamManagementModal.tsx",
                    lineNumber: 308,
                    columnNumber: 9
                }, this)
        },
        {
            title: '状态',
            dataIndex: 'status',
            key: 'status',
            render: (status)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_InvitationStatus.default, {
                    status: status
                }, void 0, false, {
                    fileName: "src/pages/personal-center/components/TeamManagementModal.tsx",
                    lineNumber: 321,
                    columnNumber: 9
                }, this)
        },
        {
            title: '邀请时间',
            dataIndex: 'invitedAt',
            key: 'invitedAt',
            render: (text)=>(0, _dayjs.default)(text).format('YYYY-MM-DD HH:mm')
        },
        {
            title: '过期时间',
            dataIndex: 'expiresAt',
            key: 'expiresAt',
            render: (text)=>(0, _dayjs.default)(text).format('YYYY-MM-DD HH:mm')
        },
        {
            title: '邀请人',
            dataIndex: 'inviterName',
            key: 'inviterName'
        }
    ];
    if (hasManagePermission) invitationColumns.push({
        title: '操作',
        key: 'action',
        render: (_, record)=>/*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                children: record.canBeCancelled && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Popconfirm, {
                    title: "确定要取消这个邀请吗？",
                    onConfirm: ()=>handleCancelInvitation(record.id),
                    okText: "确定",
                    cancelText: "取消",
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                        type: "text",
                        danger: true,
                        size: "small",
                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.DeleteOutlined, {}, void 0, false, {
                            fileName: "src/pages/personal-center/components/TeamManagementModal.tsx",
                            lineNumber: 360,
                            columnNumber: 23
                        }, void 0),
                        children: "取消"
                    }, void 0, false, {
                        fileName: "src/pages/personal-center/components/TeamManagementModal.tsx",
                        lineNumber: 356,
                        columnNumber: 15
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/personal-center/components/TeamManagementModal.tsx",
                    lineNumber: 350,
                    columnNumber: 13
                }, this)
            }, void 0, false, {
                fileName: "src/pages/personal-center/components/TeamManagementModal.tsx",
                lineNumber: 348,
                columnNumber: 9
            }, this)
    });
    // 取消邀请
    const handleCancelInvitation = async (invitationId)=>{
        try {
            await _invitation.InvitationService.cancelInvitation(invitationId);
            _antd.message.success('邀请已取消');
            fetchInvitations();
        } catch (error) {
            console.error('取消邀请失败:', error);
            _antd.message.error('取消邀请失败');
        }
    };
    return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_jsxdevruntime.Fragment, {
        children: [
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Modal, {
                title: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.TeamOutlined, {}, void 0, false, {
                            fileName: "src/pages/personal-center/components/TeamManagementModal.tsx",
                            lineNumber: 388,
                            columnNumber: 13
                        }, void 0),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("span", {
                            children: [
                                "团队管理 - ",
                                team.name
                            ]
                        }, void 0, true, {
                            fileName: "src/pages/personal-center/components/TeamManagementModal.tsx",
                            lineNumber: 389,
                            columnNumber: 13
                        }, void 0)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/personal-center/components/TeamManagementModal.tsx",
                    lineNumber: 387,
                    columnNumber: 11
                }, void 0),
                open: visible,
                onCancel: onCancel,
                width: 1200,
                footer: null,
                destroyOnClose: true,
                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Tabs, {
                    defaultActiveKey: "members",
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(TabPane, {
                            tab: "团队成员",
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                title: "团队成员列表",
                                extra: hasManagePermission && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                    type: "primary",
                                    icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.PlusOutlined, {}, void 0, false, {
                                        fileName: "src/pages/personal-center/components/TeamManagementModal.tsx",
                                        lineNumber: 406,
                                        columnNumber: 27
                                    }, void 0),
                                    onClick: ()=>setInviteModalVisible(true),
                                    children: "邀请成员"
                                }, void 0, false, {
                                    fileName: "src/pages/personal-center/components/TeamManagementModal.tsx",
                                    lineNumber: 404,
                                    columnNumber: 19
                                }, void 0),
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.ProTable, {
                                    columns: memberColumns,
                                    dataSource: members,
                                    rowKey: "id",
                                    loading: loading,
                                    search: false,
                                    pagination: {
                                        showSizeChanger: true,
                                        showQuickJumper: true,
                                        showTotal: (total)=>`共 ${total} 名成员`,
                                        pageSize: 10
                                    },
                                    options: {
                                        reload: ()=>fetchMembers(),
                                        setting: false,
                                        density: false
                                    },
                                    size: "small"
                                }, void 0, false, {
                                    fileName: "src/pages/personal-center/components/TeamManagementModal.tsx",
                                    lineNumber: 414,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/personal-center/components/TeamManagementModal.tsx",
                                lineNumber: 400,
                                columnNumber: 13
                            }, this)
                        }, "members", false, {
                            fileName: "src/pages/personal-center/components/TeamManagementModal.tsx",
                            lineNumber: 399,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(TabPane, {
                            tab: "邀请记录",
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                title: "邀请记录",
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.ProTable, {
                                    columns: invitationColumns,
                                    dataSource: invitations,
                                    rowKey: "id",
                                    loading: loading,
                                    search: false,
                                    pagination: {
                                        showSizeChanger: true,
                                        showQuickJumper: true,
                                        showTotal: (total)=>`共 ${total} 条邀请`,
                                        pageSize: 10
                                    },
                                    options: {
                                        reload: ()=>fetchInvitations(),
                                        setting: false,
                                        density: false
                                    },
                                    size: "small"
                                }, void 0, false, {
                                    fileName: "src/pages/personal-center/components/TeamManagementModal.tsx",
                                    lineNumber: 438,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/personal-center/components/TeamManagementModal.tsx",
                                lineNumber: 437,
                                columnNumber: 13
                            }, this)
                        }, "invitations", false, {
                            fileName: "src/pages/personal-center/components/TeamManagementModal.tsx",
                            lineNumber: 436,
                            columnNumber: 11
                        }, this),
                        hasManagePermission && /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(TabPane, {
                            tab: "团队设置",
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Card, {
                                title: "团队设置",
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                    direction: "vertical",
                                    style: {
                                        width: '100%'
                                    },
                                    size: "large",
                                    children: [
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                                                    level: 5,
                                                    children: "基本信息"
                                                }, void 0, false, {
                                                    fileName: "src/pages/personal-center/components/TeamManagementModal.tsx",
                                                    lineNumber: 465,
                                                    columnNumber: 21
                                                }, this),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.EditOutlined, {}, void 0, false, {
                                                            fileName: "src/pages/personal-center/components/TeamManagementModal.tsx",
                                                            lineNumber: 468,
                                                            columnNumber: 31
                                                        }, void 0),
                                                        onClick: ()=>{
                                                            form.setFieldsValue({
                                                                name: team.name,
                                                                description: team.description
                                                            });
                                                            setEditModalVisible(true);
                                                        },
                                                        children: "编辑团队信息"
                                                    }, void 0, false, {
                                                        fileName: "src/pages/personal-center/components/TeamManagementModal.tsx",
                                                        lineNumber: 467,
                                                        columnNumber: 23
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "src/pages/personal-center/components/TeamManagementModal.tsx",
                                                    lineNumber: 466,
                                                    columnNumber: 21
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/personal-center/components/TeamManagementModal.tsx",
                                            lineNumber: 464,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                                            children: [
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Title, {
                                                    level: 5,
                                                    type: "danger",
                                                    children: "危险操作"
                                                }, void 0, false, {
                                                    fileName: "src/pages/personal-center/components/TeamManagementModal.tsx",
                                                    lineNumber: 483,
                                                    columnNumber: 21
                                                }, this),
                                                /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Alert, {
                                                    message: "删除团队",
                                                    description: "删除团队后，所有团队数据将无法恢复，请谨慎操作。",
                                                    type: "warning",
                                                    showIcon: true,
                                                    action: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                                        danger: true,
                                                        icon: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_icons.DeleteOutlined, {}, void 0, false, {
                                                            fileName: "src/pages/personal-center/components/TeamManagementModal.tsx",
                                                            lineNumber: 492,
                                                            columnNumber: 33
                                                        }, void 0),
                                                        onClick: ()=>setDeleteModalVisible(true),
                                                        children: "删除团队"
                                                    }, void 0, false, {
                                                        fileName: "src/pages/personal-center/components/TeamManagementModal.tsx",
                                                        lineNumber: 490,
                                                        columnNumber: 25
                                                    }, void 0)
                                                }, void 0, false, {
                                                    fileName: "src/pages/personal-center/components/TeamManagementModal.tsx",
                                                    lineNumber: 484,
                                                    columnNumber: 21
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "src/pages/personal-center/components/TeamManagementModal.tsx",
                                            lineNumber: 482,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "src/pages/personal-center/components/TeamManagementModal.tsx",
                                    lineNumber: 463,
                                    columnNumber: 17
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/personal-center/components/TeamManagementModal.tsx",
                                lineNumber: 462,
                                columnNumber: 15
                            }, this)
                        }, "settings", false, {
                            fileName: "src/pages/personal-center/components/TeamManagementModal.tsx",
                            lineNumber: 461,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/personal-center/components/TeamManagementModal.tsx",
                    lineNumber: 398,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "src/pages/personal-center/components/TeamManagementModal.tsx",
                lineNumber: 385,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Modal, {
                title: "邀请成员",
                open: inviteModalVisible,
                onCancel: ()=>{
                    setInviteModalVisible(false);
                    inviteForm.resetFields();
                },
                footer: null,
                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form, {
                    form: inviteForm,
                    layout: "vertical",
                    onFinish: handleInviteMembers,
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                            name: "emails",
                            label: "邮箱地址",
                            rules: [
                                {
                                    required: true,
                                    message: '请输入邮箱地址'
                                }
                            ],
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(TextArea, {
                                rows: 4,
                                placeholder: "请输入邮箱地址，多个邮箱用逗号分隔"
                            }, void 0, false, {
                                fileName: "src/pages/personal-center/components/TeamManagementModal.tsx",
                                lineNumber: 527,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/components/TeamManagementModal.tsx",
                            lineNumber: 522,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                            name: "message",
                            label: "邀请消息（可选）",
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(TextArea, {
                                rows: 3,
                                placeholder: "输入邀请消息..."
                            }, void 0, false, {
                                fileName: "src/pages/personal-center/components/TeamManagementModal.tsx",
                                lineNumber: 537,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/components/TeamManagementModal.tsx",
                            lineNumber: 533,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                        type: "primary",
                                        htmlType: "submit",
                                        children: "发送邀请"
                                    }, void 0, false, {
                                        fileName: "src/pages/personal-center/components/TeamManagementModal.tsx",
                                        lineNumber: 545,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                        onClick: ()=>{
                                            setInviteModalVisible(false);
                                            inviteForm.resetFields();
                                        },
                                        children: "取消"
                                    }, void 0, false, {
                                        fileName: "src/pages/personal-center/components/TeamManagementModal.tsx",
                                        lineNumber: 548,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/personal-center/components/TeamManagementModal.tsx",
                                lineNumber: 544,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/components/TeamManagementModal.tsx",
                            lineNumber: 543,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/personal-center/components/TeamManagementModal.tsx",
                    lineNumber: 517,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "src/pages/personal-center/components/TeamManagementModal.tsx",
                lineNumber: 508,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Modal, {
                title: "编辑团队信息",
                open: editModalVisible,
                onCancel: ()=>{
                    setEditModalVisible(false);
                    form.resetFields();
                },
                footer: null,
                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form, {
                    form: form,
                    layout: "vertical",
                    onFinish: handleUpdateTeam,
                    children: [
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                            name: "name",
                            label: "团队名称",
                            rules: [
                                {
                                    required: true,
                                    message: '请输入团队名称'
                                }
                            ],
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                                placeholder: "请输入团队名称"
                            }, void 0, false, {
                                fileName: "src/pages/personal-center/components/TeamManagementModal.tsx",
                                lineNumber: 579,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/components/TeamManagementModal.tsx",
                            lineNumber: 574,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                            name: "description",
                            label: "团队描述",
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(TextArea, {
                                rows: 4,
                                placeholder: "请输入团队描述"
                            }, void 0, false, {
                                fileName: "src/pages/personal-center/components/TeamManagementModal.tsx",
                                lineNumber: 586,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/components/TeamManagementModal.tsx",
                            lineNumber: 582,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Form.Item, {
                            children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Space, {
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                        type: "primary",
                                        htmlType: "submit",
                                        loading: updating,
                                        children: "保存"
                                    }, void 0, false, {
                                        fileName: "src/pages/personal-center/components/TeamManagementModal.tsx",
                                        lineNumber: 594,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                                        onClick: ()=>{
                                            setEditModalVisible(false);
                                            form.resetFields();
                                        },
                                        children: "取消"
                                    }, void 0, false, {
                                        fileName: "src/pages/personal-center/components/TeamManagementModal.tsx",
                                        lineNumber: 597,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/personal-center/components/TeamManagementModal.tsx",
                                lineNumber: 593,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "src/pages/personal-center/components/TeamManagementModal.tsx",
                            lineNumber: 592,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "src/pages/personal-center/components/TeamManagementModal.tsx",
                    lineNumber: 569,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "src/pages/personal-center/components/TeamManagementModal.tsx",
                lineNumber: 560,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Modal, {
                title: "删除团队",
                open: deleteModalVisible,
                onCancel: ()=>{
                    setDeleteModalVisible(false);
                    setDeleteConfirmText('');
                },
                footer: [
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                        onClick: ()=>{
                            setDeleteModalVisible(false);
                            setDeleteConfirmText('');
                        },
                        children: "取消"
                    }, "cancel", false, {
                        fileName: "src/pages/personal-center/components/TeamManagementModal.tsx",
                        lineNumber: 617,
                        columnNumber: 11
                    }, void 0),
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Button, {
                        type: "primary",
                        danger: true,
                        loading: deleting,
                        disabled: deleteConfirmText !== team.name,
                        onClick: handleDeleteTeam,
                        children: "确认删除"
                    }, "delete", false, {
                        fileName: "src/pages/personal-center/components/TeamManagementModal.tsx",
                        lineNumber: 626,
                        columnNumber: 11
                    }, void 0)
                ],
                children: [
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Alert, {
                        message: "警告",
                        description: "删除团队是不可逆的操作，将会删除所有团队数据。",
                        type: "error",
                        showIcon: true,
                        style: {
                            marginBottom: 16
                        }
                    }, void 0, false, {
                        fileName: "src/pages/personal-center/components/TeamManagementModal.tsx",
                        lineNumber: 638,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                        children: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                children: [
                                    "请输入团队名称 ",
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(Text, {
                                        code: true,
                                        children: team.name
                                    }, void 0, false, {
                                        fileName: "src/pages/personal-center/components/TeamManagementModal.tsx",
                                        lineNumber: 647,
                                        columnNumber: 25
                                    }, this),
                                    " 来确认删除："
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/personal-center/components/TeamManagementModal.tsx",
                                lineNumber: 647,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Input, {
                                value: deleteConfirmText,
                                onChange: (e)=>setDeleteConfirmText(e.target.value),
                                placeholder: `请输入 ${team.name}`,
                                style: {
                                    marginTop: 8
                                }
                            }, void 0, false, {
                                fileName: "src/pages/personal-center/components/TeamManagementModal.tsx",
                                lineNumber: 648,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/personal-center/components/TeamManagementModal.tsx",
                        lineNumber: 646,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "src/pages/personal-center/components/TeamManagementModal.tsx",
                lineNumber: 609,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true);
};
_s(TeamManagementModal, "BitajdFRrK4sdaMMchwOFeKvCF8=", false, function() {
    return [
        _antd.Form.useForm,
        _antd.Form.useForm
    ];
});
_c = TeamManagementModal;
var _default = TeamManagementModal;
var _c;
$RefreshReg$(_c, "TeamManagementModal");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
"src/pages/personal-center/index.tsx": function (module, exports, __mako_require__){
"use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "default", {
    enumerable: true,
    get: function() {
        return _default;
    }
});
var _interop_require_default = __mako_require__("@swc/helpers/_/_interop_require_default");
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
var _jsxdevruntime = __mako_require__("node_modules/react/jsx-dev-runtime.js");
var _max = __mako_require__("src/.umi/exports.ts");
var _antd = __mako_require__("node_modules/antd/es/index.js");
var _procomponents = __mako_require__("node_modules/@ant-design/pro-components/es/index.js");
var _react = /*#__PURE__*/ _interop_require_default._(__mako_require__("node_modules/react/index.js"));
var _FloatButton = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/components/FloatButton/index.tsx"));
var _TeamListCard = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/personal-center/TeamListCard.tsx"));
var _TodoManagement = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/personal-center/TodoManagement.tsx"));
var _PersonalInfo = /*#__PURE__*/ _interop_require_default._(__mako_require__("src/pages/personal-center/PersonalInfo.tsx"));
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
var _s = $RefreshSig$();
/**
 * 个人中心页面组件
 *
 * 这是用户的个人中心主页面，提供用户信息管理、团队管理、待办事项等功能。
 * 是用户进行个人设置和团队操作的主要入口页面。
 *
 * 页面功能：
 * 1. 用户个人信息展示和编辑
 * 2. 团队列表显示和团队切换
 * 3. 个人待办事项管理
 * 4. 全局浮动操作按钮
 *
 * 页面结构：
 * - 左列：个人信息、数据概览、上次登录信息、团队列表（响应式布局）
 * - 右列：待办事项管理（响应式布局）
 * - 浮动：全局操作按钮
 *
 * 权限控制：
 * - 需要用户登录才能访问
 * - 自动检查登录状态并重定向
 * - 支持登录状态变化的实时响应
 *
 * 响应式设计：
 * - 移动端：垂直堆叠布局
 * - 桌面端：左右分栏布局
 * - 自适应不同屏幕尺寸
 */ const PersonalCenterPage = ()=>{
    _s();
    /**
   * 全局状态管理
   *
   * 从UmiJS全局状态中获取用户信息和加载状态：
   * - initialState: 包含用户和团队信息的全局状态
   * - loading: 全局状态的加载状态
   */ const { initialState, loading } = (0, _max.useModel)('@@initialState');
    /**
   * 加载状态处理
   *
   * 当全局状态正在初始化时，显示加载界面。
   * 这确保了用户在状态加载完成前看到友好的加载提示。
   */ if (loading) return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
        style: {
            minHeight: '100vh',
            background: '#f5f8ff',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center'
        },
        children: [
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Spin, {
                size: "large"
            }, void 0, false, {
                fileName: "src/pages/personal-center/index.tsx",
                lineNumber: 66,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                style: {
                    marginLeft: 16
                },
                children: "正在加载用户信息..."
            }, void 0, false, {
                fileName: "src/pages/personal-center/index.tsx",
                lineNumber: 67,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "src/pages/personal-center/index.tsx",
        lineNumber: 57,
        columnNumber: 7
    }, this);
    /**
   * 登录状态检查已由应用级路由守卫处理
   *
   * 移除页面级别的登录检查，避免与应用级路由守卫冲突。
   * 应用级路由守卫在 app.tsx 中的 onPageChange 函数已经处理了
   * 用户登录状态检查和重定向逻辑，无需在页面组件中重复检查。
   *
   * 这样可以避免登录成功后的状态更新时序问题，确保用户
   * 一次登录成功后能够正常访问个人中心页面。
   */ return /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_jsxdevruntime.Fragment, {
        children: [
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)("div", {
                style: {
                    minHeight: '100vh',
                    background: '#f5f8ff',
                    padding: '12px 12px 24px 12px'
                },
                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_procomponents.ProCard, {
                    style: {
                        width: '100%',
                        minHeight: 'calc(100vh - 48px)',
                        borderRadius: '12px',
                        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)'
                    },
                    bodyStyle: {
                        padding: '24px'
                    },
                    children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Row, {
                        gutter: [
                            24,
                            16
                        ],
                        style: {
                            margin: 0
                        },
                        children: [
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                xs: 24,
                                sm: 24,
                                md: 24,
                                lg: 12,
                                xl: 12,
                                xxl: 12,
                                children: [
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_PersonalInfo.default, {}, void 0, false, {
                                        fileName: "src/pages/personal-center/index.tsx",
                                        lineNumber: 144,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_TeamListCard.default, {}, void 0, false, {
                                        fileName: "src/pages/personal-center/index.tsx",
                                        lineNumber: 147,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "src/pages/personal-center/index.tsx",
                                lineNumber: 135,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_antd.Col, {
                                xs: 24,
                                sm: 24,
                                md: 24,
                                lg: 12,
                                xl: 12,
                                xxl: 12,
                                children: /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_TodoManagement.default, {}, void 0, false, {
                                    fileName: "src/pages/personal-center/index.tsx",
                                    lineNumber: 166,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "src/pages/personal-center/index.tsx",
                                lineNumber: 158,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "src/pages/personal-center/index.tsx",
                        lineNumber: 122,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "src/pages/personal-center/index.tsx",
                    lineNumber: 102,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "src/pages/personal-center/index.tsx",
                lineNumber: 86,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, _jsxdevruntime.jsxDEV)(_FloatButton.default, {}, void 0, false, {
                fileName: "src/pages/personal-center/index.tsx",
                lineNumber: 182,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true);
};
_s(PersonalCenterPage, "J38jDe63PMq+vZFAyaRULBMLxho=", false, function() {
    return [
        _max.useModel
    ];
});
_c = PersonalCenterPage;
var _default = PersonalCenterPage;
var _c;
$RefreshReg$(_c, "PersonalCenterPage");
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
"src/services/todo.ts": function (module, exports, __mako_require__){
/**
 * TODO服务
 */ "use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
__mako_require__.d(exports, "TodoService", {
    enumerable: true,
    get: function() {
        return TodoService;
    }
});
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
var _request = __mako_require__("src/utils/request.ts");
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
class TodoService {
    /**
   * 获取用户的TODO列表
   */ static async getUserTodos() {
        const response = await _request.apiRequest.get('/todos');
        return response.data;
    }
    /**
   * 创建TODO
   */ static async createTodo(request) {
        const response = await _request.apiRequest.post('/todos', request);
        return response.data;
    }
    /**
   * 更新TODO
   */ static async updateTodo(id, request) {
        const response = await _request.apiRequest.put(`/todos/${id}`, request);
        return response.data;
    }
    /**
   * 删除TODO
   */ static async deleteTodo(id) {
        await _request.apiRequest.delete(`/todos/${id}`);
    }
    /**
   * 获取TODO统计信息
   */ static async getTodoStats() {
        const response = await _request.apiRequest.get('/todos/stats');
        return response.data;
    }
}
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
"src/utils/teamSelectionUtils.ts": function (module, exports, __mako_require__){
/**
 * 团队选择状态管理工具函数
 * 用于跟踪用户是否已经主动选择过团队，以区分初始登录状态和主动选择状态
 */ // 团队选择历史的本地存储键
"use strict";
__mako_require__.d(exports, "__esModule", {
    value: true
});
function _export(target, all) {
    for(var name in all)Object.defineProperty(target, name, {
        enumerable: true,
        get: all[name]
    });
}
__mako_require__.e(exports, {
    clearUserTeamSelectionHistory: function() {
        return clearUserTeamSelectionHistory;
    },
    getAllTeamSelectionKeys: function() {
        return getAllTeamSelectionKeys;
    },
    getUserTeamSelectionHistory: function() {
        return getUserTeamSelectionHistory;
    },
    hasUserSelectedTeam: function() {
        return hasUserSelectedTeam;
    },
    recordTeamSelection: function() {
        return recordTeamSelection;
    }
});
var _interop_require_wildcard = __mako_require__("@swc/helpers/_/_interop_require_wildcard");
var _reactrefresh = /*#__PURE__*/ _interop_require_wildcard._(__mako_require__("node_modules/@umijs/mako/node_modules/react-refresh/runtime.js"));
var prevRefreshReg;
var prevRefreshSig;
prevRefreshReg = self.$RefreshReg$;
prevRefreshSig = self.$RefreshSig$;
self.$RefreshReg$ = (type, id)=>{
    _reactrefresh.register(type, module.id + id);
};
self.$RefreshSig$ = _reactrefresh.createSignatureFunctionForTransform;
const TEAM_SELECTION_KEY = 'user_team_selection_history';
const getUserTeamSelectionHistory = (userId)=>{
    try {
        const history = localStorage.getItem(`${TEAM_SELECTION_KEY}_${userId}`);
        if (history) return new Set(JSON.parse(history));
    } catch (error) {
        console.error('获取团队选择历史失败:', error);
    }
    return new Set();
};
const recordTeamSelection = (userId, teamId)=>{
    try {
        const history = getUserTeamSelectionHistory(userId);
        history.add(teamId);
        localStorage.setItem(`${TEAM_SELECTION_KEY}_${userId}`, JSON.stringify([
            ...history
        ]));
        console.log(`记录团队选择: 用户${userId}选择了团队${teamId}`);
    } catch (error) {
        console.error('记录团队选择历史失败:', error);
    }
};
const hasUserSelectedTeam = (userId, teamId)=>{
    const history = getUserTeamSelectionHistory(userId);
    return history.has(teamId);
};
const clearUserTeamSelectionHistory = (userId)=>{
    try {
        localStorage.removeItem(`${TEAM_SELECTION_KEY}_${userId}`);
        console.log(`清除用户${userId}的团队选择历史`);
    } catch (error) {
        console.error('清除团队选择历史失败:', error);
    }
};
const getAllTeamSelectionKeys = ()=>{
    const keys = [];
    for(let i = 0; i < localStorage.length; i++){
        const key = localStorage.key(i);
        if (key && key.startsWith(TEAM_SELECTION_KEY)) keys.push(key);
    }
    return keys;
};
if (prevRefreshReg) self.$RefreshReg$ = prevRefreshReg;
if (prevRefreshSig) self.$RefreshSig$ = prevRefreshSig;
function registerClassComponent(filename, moduleExports) {
    for(const key in moduleExports)try {
        if (key === "__esModule") continue;
        const exportValue = moduleExports[key];
        if (_reactrefresh.isLikelyComponentType(exportValue) && exportValue.prototype && exportValue.prototype.isReactComponent) _reactrefresh.register(exportValue, filename + " " + key);
    } catch (e) {}
}
function $RefreshIsReactComponentLike$(moduleExports) {
    if (_reactrefresh.isLikelyComponentType(moduleExports || moduleExports.default)) return true;
    for(var key in moduleExports)try {
        if (_reactrefresh.isLikelyComponentType(moduleExports[key])) return true;
    } catch (e) {}
    return false;
}
registerClassComponent(module.id, module.exports);
if ($RefreshIsReactComponentLike$(module.exports)) {
    module.meta.hot.accept();
    _reactrefresh.performReactRefresh();
}

},
 }]);
//# sourceMappingURL=src_pages_personal-center_index_tsx-async.js.map